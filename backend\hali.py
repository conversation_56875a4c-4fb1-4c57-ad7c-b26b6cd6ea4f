from fastapi import FastAPI, WebSocket
from piper.voice import PiperVoice
import re
import numpy as np

app = FastAPI()

# Load Piper model
MODEL_PATH = "./voices/ru/ru_RU-ruslan-medium.onnx"
voice = PiperVoice.load(MODEL_PATH)

# Buffer for leftover text
leftover_text = ""

# Regex for SSML-like tags
TAG_PATTERN = re.compile(r"<(pause|rate|emotion)(?:=([^>]+))?>")

# Russian stressed vowels we’ll detect (ó indicates stress)
STRESSED_VOWELS = "áéíóúý"  # you can expand if needed

def extract_stress(text: str):
    """Return cleaned text without stress markers and positions of stressed characters."""
    stressed_positions = []
    cleaned_text = ""
    for i, char in enumerate(text):
        if char in STRESSED_VOWELS:
            stressed_positions.append(len(cleaned_text))
            cleaned_text += char.lower()  # remove accent for Piper
        else:
            cleaned_text += char
    return cleaned_text, stressed_positions

def synthesize_with_tags(text: str):
    """Parse SSML-like tags and yield audio chunks, handling stress markers."""
    # First, extract stress markers
    text, stressed_positions = extract_stress(text)

    pos = 0
    current_rate = 1.0
    current_noise = voice.config.noise_scale
    current_noise_w = voice.config.noise_w

    for match in TAG_PATTERN.finditer(text):
        chunk = text[pos:match.start()].strip()
        if chunk:
            # Convert chunk to phonemes
            sentence_phonemes = voice.phonemize(chunk)
            for phonemes in sentence_phonemes:
                phoneme_ids = voice.phonemes_to_ids(phonemes)

                # Determine which phonemes correspond to stressed positions
                stressed_phoneme_indices = []
                char_count = 0
                for i, phoneme in enumerate(phonemes):
                    if char_count in stressed_positions:
                        stressed_phoneme_indices.append(i)
                    char_count += 1

                # Synthesize per phoneme with stress emphasis
                for i, phoneme_id in enumerate([phoneme_ids]):
                    if i in stressed_phoneme_indices:
                        length = current_rate * 1.3
                        noise = 0.8
                    else:
                        length = current_rate
                        noise = current_noise

                    audio_bytes = voice.synthesize_ids_to_raw(
                        phoneme_id,
                        length_scale=length,
                        noise_scale=noise,
                        noise_w=current_noise_w,
                    )
                    yield audio_bytes

        tag, value = match.groups()
        if tag == "pause":
            ms = int(value or 500)
            num_silence_samples = int((ms / 1000.0) * voice.config.sample_rate)
            silence_bytes = bytes(num_silence_samples * 2)
            yield silence_bytes
        elif tag == "rate":
            if value == "slow":
                current_rate = 1.5
            elif value == "fast":
                current_rate = 0.7
            else:
                try:
                    current_rate = float(value)
                except:
                    current_rate = 1.0
        elif tag == "emotion":
            if value == "calm":
                current_noise = 0.2
                current_noise_w = 0.2
            elif value == "excited":
                current_noise = 1.0
                current_noise_w = 1.0
            else:
                current_noise = voice.config.noise_scale
                current_noise_w = voice.config.noise_w

        pos = match.end()

    # Process any remaining text
    tail = text[pos:].strip()
    if tail:
        sentence_phonemes = voice.phonemize(tail)
        for phonemes in sentence_phonemes:
            phoneme_ids = voice.phonemes_to_ids(phonemes)
            stressed_phoneme_indices = []
            char_count = 0
            for i, phoneme in enumerate(phonemes):
                if char_count in stressed_positions:
                    stressed_phoneme_indices.append(i)
                char_count += 1

            for i, phoneme_id in enumerate([phoneme_ids]):
                if i in stressed_phoneme_indices:
                    length = current_rate * 1.3
                    noise = 0.8
                else:
                    length = current_rate
                    noise = current_noise

                audio_bytes = voice.synthesize_ids_to_raw(
                    phoneme_id,
                    length_scale=length,
                    noise_scale=noise,
                    noise_w=current_noise_w,
                )
                yield audio_bytes


@app.websocket("/tts")
async def tts_endpoint(websocket: WebSocket):
    global leftover_text
    await websocket.accept()
    try:
        while True:
            new_text = await websocket.receive_text()
            if not new_text:
                continue

            # Combine leftover buffer with new text
            full_text = leftover_text + " " + new_text
            leftover_text = ""  # reset buffer

            print(f"Received text: {full_text}")

            # Stream synthesized audio
            async for chunk in async_generator(synthesize_with_tags(full_text)):
                await websocket.send_bytes(chunk)

    except Exception as e:
        print(f"Error: {e}")
    finally:
        await websocket.close()


async def async_generator(sync_gen):
    """Helper to convert sync generator into async for streaming."""
    for item in sync_gen:
        yield item
