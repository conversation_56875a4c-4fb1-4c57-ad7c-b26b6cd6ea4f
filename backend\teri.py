from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from piper.voice import PiperVoice
import re
import numpy as np

app = FastAPI()

# Load Piper model
MODEL_PATH = "./voices/ru/ru_RU-ruslan-medium.onnx"
voice = PiperVoice.load(MODEL_PATH)

# Buffer for unfinished text
leftover_text = ""

# Regex for SSML-like tags
TAG_PATTERN = re.compile(r"<(pause|rate|emotion)(?:=([^>]+))?>")
SENTENCE_END = re.compile(r"[.!?]$")     

def synthesize_with_tags(text: str):
    """Parse SSML-like tags and yield audio chunks."""
    pos = 0
    current_rate = 1.0
    current_noise = voice.config.noise_scale
    current_noise_w = voice.config.noise_w

    for match in TAG_PATTERN.finditer(text):
        # Synthesize the text before the tag
        chunk = text[pos:match.start()].strip()
        if chunk:
            for audio_bytes in voice.synthesize_stream_raw(
                chunk,
                length_scale=current_rate,
                noise_scale=current_noise,
                noise_w=current_noise_w,
            ):
                yield audio_bytes

        tag, value = match.groups()

        if tag == "pause":
            ms = int(value or 500)
            num_silence_samples = int((ms / 1000.0) * voice.config.sample_rate)
            silence_bytes = bytes(num_silence_samples * 2)  # 16-bit PCM
            yield silence_bytes

        elif tag == "rate":
            if value == "slow":
                current_rate = 1.5
            elif value == "fast":
                current_rate = 0.7
            else:
                try:
                    current_rate = float(value)
                except:
                    current_rate = 1.0

        elif tag == "emotion":
            if value == "calm":
                current_noise = 0.2
                current_noise_w = 0.2
            elif value == "excited":
                current_noise = 1.0
                current_noise_w = 1.0
            else:
                current_noise = voice.config.noise_scale
                current_noise_w = voice.config.noise_w

        pos = match.end()

    # Synthesize any trailing text
    tail = text[pos:].strip()
    if tail:
        for audio_bytes in voice.synthesize_stream_raw(
            tail,
            length_scale=current_rate,
            noise_scale=current_noise,
            noise_w=current_noise_w,
        ):
            yield audio_bytes


@app.websocket("/tts")
async def tts_endpoint(websocket: WebSocket):
    global leftover_text
    await websocket.accept()
    try:
        while True:
            new_text = await websocket.receive_text()
            if not new_text:
                continue

            # 1. append new text to whatever was left
            buffer = (leftover_text + " " + new_text).strip()
            leftover_text = ""

            # 2. split into sentences
            sentences = re.split(r"(?<=[.!?])\s*", buffer)

            # 3. last element is either a complete sentence or unfinished
            if sentences and not SENTENCE_END.search(sentences[-1]):
                leftover_text = sentences.pop()   # keep for next round

            if not sentences:          # nothing complete yet
                continue

            # one single string to synthesise
            text_to_speak = "<pause=500> ".join(sentences)          # or add "<pause=500>" here if you want
            async for audio in async_generator(synthesize_with_tags(text_to_speak)):
                await websocket.send_bytes(audio)

    except WebSocketDisconnect:
        print("Client disconnected")
    except Exception as e:
        print("TTS error:", e)


async def async_generator(sync_gen):
    """Helper to convert a sync generator into async for streaming."""
    for item in sync_gen:
        yield item
