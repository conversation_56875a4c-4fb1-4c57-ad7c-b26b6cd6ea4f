module.exports = [
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[project]/src/lib/utils.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "cn",
    ()=>cn
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
}),
"[project]/src/components/avatar.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BotIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bot.js [app-ssr] (ecmascript) <export default as BotIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$round$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user-round.js [app-ssr] (ecmascript) <export default as User2Icon>");
'use client';
;
;
;
function Avatar({ avatarType = 'user', size = 'md' }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('rounded-full bg-stone-600 flex justify-center items-center', size === 'sm' && 'h-10 w-10', size === 'md' && 'h-16 w-16', size === 'lg' && 'h-24 w-24'),
        children: avatarType === 'user' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2d$round$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User2Icon$3e$__["User2Icon"], {
            className: "text-white"
        }, void 0, false, {
            fileName: "[project]/src/components/avatar.tsx",
            lineNumber: 23,
            columnNumber: 13
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BotIcon$3e$__["BotIcon"], {
            className: "text-white"
        }, void 0, false, {
            fileName: "[project]/src/components/avatar.tsx",
            lineNumber: 25,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/avatar.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = Avatar;
}),
"[project]/src/hooks/use-chat.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>useChat
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function useChat() {
    const [messages, setMessages] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState([]);
    function addMessage({ id, role, content }) {
        setMessages((prevMessages)=>[
                ...prevMessages,
                {
                    id,
                    role,
                    content
                }
            ]);
    }
    function clearMessages() {
        setMessages([]);
    }
    function editMessage(id, newContent) {
        setMessages((prevMessages)=>prevMessages.map((msg)=>msg.id === id ? {
                    ...msg,
                    content: newContent
                } : msg));
    }
    return {
        messages,
        addMessage,
        clearMessages,
        editMessage
    };
}
}),
"[externals]/node:crypto [external] (node:crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}),
"[project]/src/lib/ario/errors.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
;
}),
"[project]/src/lib/ario/state.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
__turbopack_context__.s([
    "isActiveState",
    ()=>isActiveState,
    "isInactiveState",
    ()=>isInactiveState,
    "isWebSocketState",
    ()=>isWebSocketState
]);
const recorderStates = [
    'Init',
    'RequestingMedia',
    'OpeningWebSocket',
    'Running',
    'FinishingProcessing',
    'Finished',
    'Error',
    'Canceled'
];
const inactiveStates = [
    'Init',
    'Finished',
    'Error',
    'Canceled'
];
const activeStates = [
    'RequestingMedia',
    'OpeningWebSocket',
    'Running',
    'FinishingProcessing'
];
const websocketStates = [
    'OpeningWebSocket',
    'Running',
    'FinishingProcessing'
];
function isInactiveState(state) {
    return inactiveStates.includes(state);
}
function isActiveState(state) {
    return activeStates.includes(state);
}
function isWebSocketState(state) {
    return websocketStates.includes(state);
}
}),
"[project]/src/lib/ario/sst-client.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/* eslint-disable @typescript-eslint/no-unused-vars */ __turbopack_context__.s([
    "RecordTranscribe",
    ()=>RecordTranscribe,
    "SonioxClient",
    ()=>SonioxClient
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$state$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ario/state.ts [app-ssr] (ecmascript)");
;
const defaultWebsocketUri = 'wss://stt-rt.soniox.com/transcribe-websocket';
const defaultBufferQueueSize = 1000;
const recorderTimeSliceMs = 120;
const finalizeMessage = '{ "type": "finalize" }';
const getDefaultSonioxClientOptions = ()=>({
        apiKey: '',
        bufferQueueSize: defaultBufferQueueSize
    });
const defaultAudioConstraints = {
    echoCancellation: false,
    noiseSuppression: false,
    autoGainControl: false,
    channelCount: 1,
    sampleRate: 44100
};
class SonioxClient {
    static isSupported = Boolean('WebSocket' in window && navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    _state = 'Init';
    _options;
    _audioOptions;
    _websocket;
    _mediaRecorder;
    _queuedMessages = [];
    /**
   * SonioxClient connects to the Soniox Speech-to-Text API for real-time speech-to-text transcription and translation.
   * It provides a simple API for starting and stopping the transcription, as well as handling the transcription results.
   *
   * @example
   * const sonioxClient = new SonioxClient({
   *   apiKey: '<SONIOX_API_KEY>',
   *   onPartialResult: (result) => {
   *     console.log('partial result', result.text);
   *   },
   * });
   * sonioxClient.start();
   */ constructor(options){
        if (!SonioxClient.isSupported) {
            throw 'airo Speech-to-Text is not supported on this browser.';
        }
        this._options = {
            ...getDefaultSonioxClientOptions(),
            ...options
        };
        this._audioOptions = null;
        this._websocket = null;
        this._mediaRecorder = null;
    }
    _hasCallback = (name)=>{
        return this._options[name] != null || this._audioOptions?.[name] != null;
    };
    _callback = (name, ...args)=>{
        // @ts-ignore
        this._options[name]?.(...args);
        // @ts-ignore
        this._audioOptions?.[name]?.(...args);
    };
    _setState(newState) {
        const oldState = this._state;
        this._state = newState;
        this._callback('onStateChange', {
            oldState,
            newState
        });
    }
    get state() {
        return this._state;
    }
    /**
   * Start transcription. You can pass options to configure the transcription settings, source and callbacks.
   */ start = async (audioOptions)=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$state$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isActiveState"])(this._state)) {
            throw new Error('SonioxClient is already active');
        }
        this._audioOptions = {
            ...audioOptions
        };
        let stream = undefined;
        if (audioOptions.stream != null) {
            // User has provided a stream, don't request microphone access, simply transcribe the stream.
            // We need to clone the stream so internal stopping doesn't affect the user's stream.
            stream = audioOptions.stream.clone();
        } else {
            // Stream not given, request stream from microphone.
            this._setState('RequestingMedia');
            try {
                // Request microphone access and get stream
                stream = await navigator.mediaDevices.getUserMedia({
                    audio: this._audioOptions.audioConstraints ? this._audioOptions.audioConstraints : defaultAudioConstraints
                });
            } catch (e) {
                this._onError('get_user_media_failed', e?.toString());
            }
        }
        // Mostly here to make typescript happy
        if (stream == null) {
            throw new Error('Failed to create stream');
        }
        // New media stream
        this._mediaRecorder = new MediaRecorder(stream, audioOptions.mediaRecorderOptions ? audioOptions.mediaRecorderOptions : {});
        // Start collecting data
        this._queuedMessages = [];
        this._mediaRecorder.addEventListener('dataavailable', this._onMediaRecorderData);
        this._mediaRecorder.addEventListener('error', this._onMediaRecorderError);
        this._mediaRecorder.addEventListener('pause', this._onMediaRecorderPause);
        this._mediaRecorder.addEventListener('stop', this._onMediaRecorderStop);
        // Start recording
        this._mediaRecorder.start(recorderTimeSliceMs);
        // Open websocket
        this._setState('OpeningWebSocket');
        this._websocket = new WebSocket(this._options.webSocketUri ?? defaultWebsocketUri);
        this._websocket.addEventListener('open', this._onWebSocketOpen);
        this._websocket.addEventListener('error', this._onWebSocketError);
        this._websocket.addEventListener('message', this._onWebSocketMessage);
    };
    /**
   * Stop transcription. Stopping transcription will send stop signal to the API and wait for the final results to be received.
   * Only after the final results are received, the transcription will be finished. If you want to cancel the transcription immediately,
   * (for example, on component unmount), you should probably use the `cancel()` method instead.
   */ stop = ()=>{
        if (this._state == 'RequestingMedia' || this._state == 'OpeningWebSocket') {
            this._closeResources();
            this._handleFinished();
        } else if (this._state == 'Running') {
            // Finished recording, waiting for last events from api
            this._setState('FinishingProcessing');
            this._closeSource();
            // Send empty message to api to indicate that we're done
            this._websocket?.send('');
        }
    };
    /**
   * Cancel transcription. Cancelling transcription will stop the transcription immediately and close the resources.
   * For user initiated cancellation, you should probably use the `stop()` method instead.
   */ cancel = ()=>{
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$state$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isInactiveState"])(this._state)) {
            this._closeResources();
            this._setState('Canceled');
        }
    };
    /**
   * Trigger finalize. This will finalize all non-final tokens.
   */ finalize = ()=>{
        if (this._state == 'RequestingMedia' || this._state == 'OpeningWebSocket') {
            // Still waiting for websocket to open, queue the event
            if (this._queuedMessages.length < (this._options.bufferQueueSize ?? defaultBufferQueueSize)) {
                this._queuedMessages.push(finalizeMessage);
            } else {
                this._onError('queue_limit_exceeded', 'Queue size exceeded before websocket connection was established.');
            }
        } else if (this._state == 'Running' || this._state == 'FinishingProcessing') {
            this._websocket?.send(finalizeMessage);
        }
    };
    // Media recorder events
    _onMediaRecorderData = async (event)=>{
        if (this._state === 'OpeningWebSocket') {
            // Still waiting for websocket to open, queue the event
            if (this._queuedMessages.length < (this._options.bufferQueueSize ?? defaultBufferQueueSize)) {
                this._queuedMessages.push(event.data);
            } else {
                this._onError('queue_limit_exceeded', 'Queue size exceeded before websocket connection was established.');
            }
        } else if (this._state === 'Running') {
            const data = await event.data.arrayBuffer();
            this._websocket?.send(data);
        }
    };
    _onMediaRecorderError = (event)=>{
        this._onError('media_recorder_error', event.error ?? 'Unknown error');
    };
    _onMediaRecorderPause = (_event)=>{
        this.stop();
    };
    _onMediaRecorderStop = (_event)=>{
        this.stop();
    };
    // Websocket events
    _onWebSocketOpen = (event)=>{
        void this._onWebSocketOpenAsync(event);
    };
    _onWebSocketOpenAsync = async (_event)=>{
        if (this._state !== 'OpeningWebSocket' || this._audioOptions == null) {
            return;
        }
        const opts = this._audioOptions;
        // If api key is getter, call it to get the key, if not, use the key directly
        let apiKey;
        if (typeof this._options.apiKey === 'function') {
            try {
                apiKey = await this._options.apiKey();
            } catch (e) {
                this._onError('api_key_fetch_failed', e?.toString());
                return;
            }
        } else {
            apiKey = this._options.apiKey;
        }
        // Check state again (might be different if state changed during api key fetching)
        if (this._state !== 'OpeningWebSocket') {
            return;
        }
        const request = {
            api_key: apiKey,
            model: opts.model,
            audio_format: opts.audioFormat ? opts.audioFormat : 'auto',
            sample_rate: opts.sampleRate,
            num_channels: opts.numChannels,
            language_hints: opts.languageHints,
            context: opts.context,
            enable_speaker_diarization: opts.enableSpeakerDiarization,
            enable_language_identification: opts.enableLanguageIdentification,
            enable_endpoint_detection: opts.enableEndpointDetection,
            translation: opts.translation,
            client_reference_id: opts.clientReferenceId
        };
        // Send initial request
        this._websocket?.send(JSON.stringify(request));
        // Send all queued messages (if any)
        for (const message of this._queuedMessages){
            this._websocket?.send(message);
        }
        this._queuedMessages = [];
        this._setState('Running');
        this._callback('onStarted');
    };
    _onWebSocketError = (_event)=>{
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$state$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isWebSocketState"])(this._state)) {
            return;
        }
        this._onError('websocket_error', 'WebSocket error occurred.');
    };
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _onWebSocketMessage = (event)=>{
        if (this._state != 'Running' && this._state != 'FinishingProcessing' || this._audioOptions == null) {
            return;
        }
        const response = JSON.parse(event.data);
        if (response.error_code != null || response.error_message != null) {
            this._onError('api_error', response.error_message, response.error_code);
            return;
        }
        this._callback('onPartialResult', response);
        if (response.finished) {
            this._handleFinished();
        }
    };
    _onError = (status, message, errorCode = undefined)=>{
        this._setState('Error');
        this._closeResources();
        if (this._hasCallback('onError')) {
            this._callback('onError', status, message ?? 'Unknown error', errorCode);
        } else {
            throw new Error(`SonioxClient error: ${status}: ${message ?? 'Unknown error'}`);
        }
    };
    _closeSource = ()=>{
        // Close media recorder
        if (this._mediaRecorder != null) {
            this._mediaRecorder.removeEventListener('dataavailable', this._onMediaRecorderData);
            this._mediaRecorder.removeEventListener('error', this._onMediaRecorderError);
            this._mediaRecorder.removeEventListener('pause', this._onMediaRecorderPause);
            this._mediaRecorder.removeEventListener('stop', this._onMediaRecorderStop);
            this._mediaRecorder.stop();
            this._mediaRecorder.stream.getTracks().forEach((track)=>track.stop());
            this._mediaRecorder = null;
        }
    };
    _closeResources = ()=>{
        this._queuedMessages = [];
        // Close websocket
        if (this._websocket != null) {
            this._websocket.removeEventListener('open', this._onWebSocketOpen);
            this._websocket.removeEventListener('error', this._onWebSocketError);
            this._websocket.removeEventListener('message', this._onWebSocketMessage);
            this._websocket.close();
            this._websocket = null;
        }
        this._closeSource();
    };
    _handleFinished() {
        this._closeResources();
        this._setState('Finished');
        this._callback('onFinished');
    }
}
const RecordTranscribe = SonioxClient;
}),
"[project]/src/lib/ario/types.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
;
}),
"[project]/src/lib/ario/index.ts [app-ssr] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ario/errors.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$sst$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ario/sst-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$state$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ario/state.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ario/types.ts [app-ssr] (ecmascript)");
;
;
;
;
}),
"[project]/src/hooks/use-transcribe.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>useTranscribe
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/ario/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$sst$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ario/sst-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
const END_TOKEN = '<end>';
function useTranscribe({ apiKey, translationConfig, onStarted, onFinished, onEndOfSpeech, onPartialResult }) {
    const sonioxClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    if (sonioxClient.current == null) {
        sonioxClient.current = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ario$2f$sst$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SonioxClient"]({
            apiKey: apiKey
        });
    }
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('Init');
    const [finalTokens, setFinalTokens] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [nonFinalTokens, setNonFinalTokens] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const startTranscription = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        setFinalTokens([]);
        setNonFinalTokens([]);
        setError(null);
        let tempFinalTokens = [];
        // First message we send contains configuration. Here we set if we set if we
        // are transcribing or translating. For translation we also set if it is
        // one-way or two-way.
        sonioxClient.current?.start({
            model: 'stt-rt-preview',
            enableLanguageIdentification: true,
            enableSpeakerDiarization: true,
            enableEndpointDetection: true,
            translation: translationConfig || undefined,
            onFinished: onFinished,
            onStarted: onStarted,
            onError: (status, message, errorCode)=>{
                setError({
                    status,
                    message,
                    errorCode
                });
            },
            onStateChange: ({ newState })=>{
                setState(newState);
            },
            // When we receive some tokens back, sort them based on their status --
            // is it final or non-final token.
            onPartialResult (result) {
                // console.log('Partial result', result);
                const newFinalTokens = [];
                const newNonFinalTokens = [];
                let endTokenReceived = false;
                for (const token of result.tokens){
                    // Ignore endpoint detection tokens
                    if (token.text === END_TOKEN) {
                        endTokenReceived = true;
                        continue;
                    }
                    if (token.is_final) {
                        newFinalTokens.push(token);
                    } else {
                        newNonFinalTokens.push(token);
                    }
                }
                const currentFinalTokens = [
                    ...tempFinalTokens,
                    ...newFinalTokens
                ];
                tempFinalTokens = currentFinalTokens;
                if (onPartialResult) {
                    onPartialResult(currentFinalTokens, newNonFinalTokens);
                }
                if (endTokenReceived) {
                    const fullText = currentFinalTokens.map((t)=>t.text).join('');
                    if (fullText.trim() && onEndOfSpeech) {
                        onEndOfSpeech(fullText);
                    }
                    tempFinalTokens = [];
                    setNonFinalTokens([]);
                    setNonFinalTokens([]);
                } else {
                    setFinalTokens(currentFinalTokens);
                }
                setNonFinalTokens(newNonFinalTokens);
            }
        });
    }, [
        onFinished,
        onStarted,
        translationConfig,
        onEndOfSpeech,
        finalTokens
    ]);
    const stopTranscription = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        sonioxClient.current?.stop();
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            sonioxClient.current?.cancel();
        };
    }, []);
    return {
        startTranscription,
        stopTranscription,
        state,
        finalTokens,
        nonFinalTokens,
        error
    };
}
}),
"[project]/src/components/call.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/avatar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MicIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/mic.js [app-ssr] (ecmascript) <export default as MicIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MicOffIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/mic-off.js [app-ssr] (ecmascript) <export default as MicOffIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PhoneIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/phone.js [app-ssr] (ecmascript) <export default as PhoneIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PhoneOffIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/phone-off.js [app-ssr] (ecmascript) <export default as PhoneOffIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$chat$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-chat.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/nanoid/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$transcribe$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-transcribe.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
function AiCall() {
    const { messages, addMessage, clearMessages, editMessage } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$chat$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const [joinedCall, setJoinedCall] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(false);
    const joinedCallRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(joinedCall);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        joinedCallRef.current = joinedCall;
    }, [
        joinedCall
    ]);
    const [isBotSpeaking, setIsBotSpeaking] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(false);
    const [isUserSpeaking, setIsUserSpeaking] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(false);
    const userMessageIdRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const botMessageIdRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const ttsWebSocketRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const audioBufferQueueRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([]);
    const isPlayingRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const [isMicOn, setIsMicOn] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(false);
    function toggleJoinCall() {
        if (!joinedCall === true) {
            addMessage({
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(8),
                role: 'system',
                content: 'You started the call'
            });
            startTranscription();
        } else {
            stopTranscription();
            addMessage({
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(8),
                role: 'system',
                content: 'You left the call'
            });
        }
        setJoinedCall(!joinedCall);
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        addMessage({
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(8),
            role: 'system',
            content: 'You joined the room'
        });
    }, []);
    const { state, finalTokens, nonFinalTokens, startTranscription, stopTranscription } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$transcribe$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
        apiKey: "4f7bdb6be03c50f473bf1bf90f929cf329f108447225452b5b831079581a66de",
        onEndOfSpeech: async (fullText)=>{
            userMessageIdRef.current = null;
            console.log('End of speech detected. Full text:', fullText);
            stopTranscription();
            if (fullText.trim()) {
                console.log("_________________end_____________________");
                await handleStreamResponse(fullText);
            }
            startTranscription();
        },
        onFinished: ()=>{
            if (joinedCallRef.current) {
                console.log("_________________finished_____________________");
            }
        },
        onPartialResult: (finalTokens, nonFinalTokens)=>{
            const liveTranscript = [
                ...finalTokens,
                ...nonFinalTokens
            ].map((t)=>t.text).join('');
            console.log('Live transcript:', liveTranscript);
            if (liveTranscript.trim()) {
                if (!userMessageIdRef.current) {
                    userMessageIdRef.current = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(8);
                    addMessage({
                        id: userMessageIdRef.current,
                        role: 'user',
                        content: liveTranscript
                    });
                } else {
                    editMessage(userMessageIdRef.current, liveTranscript);
                }
            }
        }
    });
    const handleStreamResponse = async (userText)=>{
        try {
            // Create bot message
            const botId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(8);
            botMessageIdRef.current = botId;
            addMessage({
                id: botId,
                role: 'assistant',
                content: ''
            });
            // Connect to TTS WebSocket
            const ttsWs = new WebSocket('ws://*************:8000/tts'); // Adjust URL as needed
            ttsWebSocketRef.current = ttsWs;
            let fullResponse = '';
            ttsWs.onopen = ()=>{
                console.log('TTS WebSocket connected');
            };
            ttsWs.onmessage = (event)=>{
                const audioData = event.data;
                if (audioData instanceof ArrayBuffer) {
                    audioBufferQueueRef.current.push(audioData);
                    playAudio();
                }
            };
            ttsWs.onerror = (error)=>{
                console.error('TTS WebSocket error:', error);
            };
            ttsWs.onclose = ()=>{
                console.log('TTS WebSocket closed');
                setIsBotSpeaking(false);
            };
            // Fetch streaming response from chat API
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: userText
                })
            });
            if (!response.ok) {
                throw new Error('Failed to fetch chat response');
            }
            const reader = response.body?.getReader();
            const decoder = new TextDecoder();
            if (reader) {
                let buffer = '';
                while(true){
                    const { done, value } = await reader.read();
                    if (done) break;
                    buffer += decoder.decode(value, {
                        stream: true
                    });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';
                    for (const line of lines){
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') break;
                            try {
                                const parsed = JSON.parse(data);
                                const content = parsed.choices[0]?.delta?.content || '';
                                if (content) {
                                    fullResponse += content;
                                    editMessage(botId, fullResponse);
                                    // Send to TTS
                                    if (ttsWs.readyState === WebSocket.OPEN) {
                                        ttsWs.send(content);
                                    }
                                }
                            } catch (e) {
                                console.error('Error parsing SSE data:', e);
                            }
                        }
                    }
                }
            }
            // Close TTS WebSocket after response
            if (ttsWs.readyState === WebSocket.OPEN) {
                ttsWs.close();
            }
        } catch (error) {
            console.error('Error in handleStreamResponse:', error);
        }
    };
    const playAudio = async ()=>{
        if (isPlayingRef.current || audioBufferQueueRef.current.length === 0) return;
        isPlayingRef.current = true;
        setIsBotSpeaking(true);
        if (!audioContextRef.current) {
            audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
        }
        const context = audioContextRef.current;
        while(audioBufferQueueRef.current.length > 0){
            const buffer = audioBufferQueueRef.current.shift();
            if (buffer) {
                try {
                    // Piper outputs 16-bit PCM at 22050 Hz, mono
                    const sampleRate = 22050;
                    const channels = 1;
                    const bytesPerSample = 2; // 16-bit
                    const numSamples = buffer.byteLength / bytesPerSample;
                    const audioBuffer = context.createBuffer(channels, numSamples, sampleRate);
                    // Convert 16-bit PCM to float32
                    const int16Array = new Int16Array(buffer);
                    const float32Array = new Float32Array(numSamples);
                    for(let i = 0; i < numSamples; i++){
                        float32Array[i] = int16Array[i] / 32768.0; // Normalize to -1 to 1
                    }
                    audioBuffer.copyFromChannel(float32Array, 0);
                    const source = context.createBufferSource();
                    source.buffer = audioBuffer;
                    source.connect(context.destination);
                    source.start();
                    await new Promise((resolve)=>{
                        source.onended = resolve;
                    });
                } catch (e) {
                    console.error('Error playing audio:', e);
                }
            }
        }
        isPlayingRef.current = false;
        setIsBotSpeaking(false);
    };
    const audioContextRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const analyserRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const streamRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const animationFrameIdRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const stopAudioAnalysis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (animationFrameIdRef.current) {
            cancelAnimationFrame(animationFrameIdRef.current);
            animationFrameIdRef.current = null;
        }
        if (streamRef.current) {
            streamRef.current.getTracks().forEach((track)=>track.stop());
            streamRef.current = null;
        }
        if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
            audioContextRef.current.close();
            audioContextRef.current = null;
        }
        setIsUserSpeaking(false);
    }, []);
    const startAudioAnalysis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: true
                });
                streamRef.current = stream;
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                audioContextRef.current = audioContext;
                const analyser = audioContext.createAnalyser();
                analyser.fftSize = 512;
                analyserRef.current = analyser;
                const source = audioContext.createMediaStreamSource(stream);
                source.connect(analyser);
                const checkAudio = ()=>{
                    if (!analyserRef.current) return;
                    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
                    analyserRef.current.getByteFrequencyData(dataArray);
                    let sum = 0;
                    for (const amplitude of dataArray){
                        sum += amplitude * amplitude;
                    }
                    const volume = Math.sqrt(sum / dataArray.length);
                    // Adjust this threshold to your needs
                    const speakingThreshold = 15;
                    setIsUserSpeaking(volume > speakingThreshold);
                    animationFrameIdRef.current = requestAnimationFrame(checkAudio);
                };
                checkAudio();
            } catch (err) {
                console.error('Error accessing microphone:', err);
                setIsMicOn(false); // Turn mic off in UI if permission is denied
            }
        }
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isMicOn) {
            startAudioAnalysis();
        } else {
            stopAudioAnalysis();
        }
        // Cleanup on component unmount
        return ()=>{
            stopAudioAnalysis();
        };
    }, [
        isMicOn,
        startAudioAnalysis,
        stopAudioAnalysis
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-full flex overflow-hidden",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: " bg-stone-900 h-full w-full overflow-hidden p-2 flex flex-col gap-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex gap-4 flex-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('bg-stone-700 h-full w-full overflow-hidden rounded-2xl flex justify-center items-center transition relative', isUserSpeaking && 'ring-2 ring-emerald-500'),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        avatarType: "user",
                                        size: "lg"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/call.tsx",
                                        lineNumber: 333,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute bottom-2 w-60 h-30 bg-transparent rounded-lg overflow-hidden",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative w-full h-full flex justify-center items-end overflow-hidden",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute w-full h-full z-10 bg-gradient-to-b from-stone-900/80 via-transparent to-transparent"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/call.tsx",
                                                    lineNumber: 341,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-full h-full flex flex-col justify-end",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-white",
                                                                children: finalTokens.map((t)=>t.text).join('')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/call.tsx",
                                                                lineNumber: 346,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-stone-400",
                                                                children: nonFinalTokens.map((t)=>t.text).join('')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/call.tsx",
                                                                lineNumber: 349,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/call.tsx",
                                                        lineNumber: 345,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/call.tsx",
                                                    lineNumber: 344,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/call.tsx",
                                            lineNumber: 340,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/call.tsx",
                                        lineNumber: 337,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/call.tsx",
                                lineNumber: 327,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('bg-stone-700 h-full w-full overflow-hidden rounded-2xl flex justify-center items-center transition relative', isBotSpeaking && 'ring-2 ring-emerald-500'),
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    avatarType: "bot",
                                    size: "lg"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/call.tsx",
                                    lineNumber: 363,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/call.tsx",
                                lineNumber: 357,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/call.tsx",
                        lineNumber: 326,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-12 rounded-2xl p-3 px-2 bg-stone-700 w-max self-center flex items-center justify-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "text-white p-3 rounded-full bg-stone-900 hover:bg-stone-800 transition cursor-pointer",
                                onClick: ()=>{
                                    setIsMicOn(!isMicOn);
                                },
                                children: isMicOn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MicIcon$3e$__["MicIcon"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/call.tsx",
                                    lineNumber: 378,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mic$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MicOffIcon$3e$__["MicOffIcon"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/call.tsx",
                                    lineNumber: 380,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/call.tsx",
                                lineNumber: 371,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('text-white p-3 rounded-full transition cursor-pointer', joinedCall ? 'bg-red-900 hover:bg-red-800' : 'bg-emerald-600 hover:bg-emerald-500'),
                                onClick: ()=>{
                                    toggleJoinCall();
                                },
                                children: joinedCall ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2d$off$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PhoneOffIcon$3e$__["PhoneOffIcon"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/call.tsx",
                                    lineNumber: 393,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PhoneIcon$3e$__["PhoneIcon"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/call.tsx",
                                    lineNumber: 395,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/call.tsx",
                                lineNumber: 383,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/call.tsx",
                        lineNumber: 370,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/call.tsx",
                lineNumber: 325,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-stone-800 h-full w-80 overflow-hidden overflow-y-auto flex flex-col p-3 gap-2",
                children: messages.map((msg)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ChatMessage, {
                        id: msg.id,
                        role: msg.role,
                        content: msg.content
                    }, msg.id, false, {
                        fileName: "[project]/src/components/call.tsx",
                        lineNumber: 403,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/call.tsx",
                lineNumber: 401,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/call.tsx",
        lineNumber: 324,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = AiCall;
const ChatMessage = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(({ id, role, content })=>{
    if (role === 'system') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-max self-center rounded-full bg-stone-700/80 px-3 py-0.5 text-xs text-stone-300",
            children: content
        }, id, false, {
            fileName: "[project]/src/components/call.tsx",
            lineNumber: 426,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('w-max max-w-[90%] rounded-lg px-3 py-1 text-xs', role === 'user' ? 'bg-amber-900 self-end text-white' : 'bg-transparent self-start text-white'),
        children: content
    }, id, false, {
        fileName: "[project]/src/components/call.tsx",
        lineNumber: 436,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
});
}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>Home
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$call$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/call.tsx [app-ssr] (ecmascript)");
"use client";
;
;
function Home() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-full overflow-y-auto overflow-x-hidden",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$call$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 9,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 8,
        columnNumber: 5
    }, this);
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__e6943e10._.js.map