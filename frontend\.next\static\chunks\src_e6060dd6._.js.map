{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hackathon/vtb-hack/arc/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hackathon/vtb-hack/arc/frontend/src/components/avatar.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { BotIcon, User2Icon } from 'lucide-react';\r\nimport React from 'react'\r\n\r\ninterface AvatarProps {\r\n    avatarType?: 'user' | 'bot';\r\n    size?: 'sm' | 'md' | 'lg';\r\n}\r\n\r\nfunction Avatar({ avatarType = 'user', size = 'md' }: AvatarProps): React.JSX.Element {\r\n  return (\r\n    <div \r\n        className={cn(\r\n            'rounded-full bg-stone-600 flex justify-center items-center',\r\n            size === 'sm' && 'h-10 w-10',\r\n            size === 'md' && 'h-16 w-16',\r\n            size === 'lg' && 'h-24 w-24',\r\n        )}  \r\n    >\r\n        {avatarType === 'user' ? (\r\n            <User2Icon className=\"text-white\" />\r\n        ) : (\r\n            <BotIcon className=\"text-white\" />\r\n        )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Avatar"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAHA;;;;AAWA,SAAS,OAAO,KAAiD;QAAjD,EAAE,aAAa,MAAM,EAAE,OAAO,IAAI,EAAe,GAAjD;IACd,qBACE,6LAAC;QACG,WAAW,IAAA,4HAAE,EACT,8DACA,SAAS,QAAQ,aACjB,SAAS,QAAQ,aACjB,SAAS,QAAQ;kBAGpB,eAAe,uBACZ,6LAAC,gOAAS;YAAC,WAAU;;;;;iCAErB,6LAAC,kNAAO;YAAC,WAAU;;;;;;;;;;;AAI/B;KAjBS;uCAmBM", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hackathon/vtb-hack/arc/frontend/src/hooks/use-chat.ts"], "sourcesContent": ["import React from \"react\";\r\n\r\ninterface ChatMessage {\r\n  id: string;\r\n  role: 'user' | 'assistant' | 'system';\r\n  content: string;\r\n}\r\n\r\nexport default function useChat() {\r\n    const [messages, setMessages] = React.useState<ChatMessage[]>([]);\r\n\r\n    function addMessage({ id, role, content }: ChatMessage) {\r\n        setMessages((prevMessages) => [...prevMessages, { id, role, content }]);\r\n    }\r\n\r\n    function clearMessages() {\r\n        setMessages([]);\r\n    }\r\n\r\n    function editMessage(id: string, newContent: string) {\r\n        setMessages((prevMessages) =>\r\n            prevMessages.map((msg) => (msg.id === id ? { ...msg, content: newContent } : msg))\r\n        );\r\n    }\r\n\r\n    return {\r\n        messages,\r\n        addMessage,\r\n        clearMessages,\r\n        editMessage,\r\n    }\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAQe,SAAS;;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,wKAAK,CAAC,QAAQ,CAAgB,EAAE;IAEhE,SAAS,WAAW,KAAkC;YAAlC,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAe,GAAlC;QAChB,YAAY,CAAC,eAAiB;mBAAI;gBAAc;oBAAE;oBAAI;oBAAM;gBAAQ;aAAE;IAC1E;IAEA,SAAS;QACL,YAAY,EAAE;IAClB;IAEA,SAAS,YAAY,EAAU,EAAE,UAAkB;QAC/C,YAAY,CAAC,eACT,aAAa,GAAG,CAAC,CAAC,MAAS,IAAI,EAAE,KAAK,KAAK;oBAAE,GAAG,GAAG;oBAAE,SAAS;gBAAW,IAAI;IAErF;IAEA,OAAO;QACH;QACA;QACA;QACA;IACJ;AACJ;GAvBwB", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hackathon/vtb-hack/arc/frontend/src/lib/ario/state.ts"], "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nconst recorderStates = [\r\n  'Init',\r\n  'RequestingMedia',\r\n  'OpeningWebSocket',\r\n  'Running',\r\n  'FinishingProcessing',\r\n  'Finished',\r\n  'Error',\r\n  'Canceled',\r\n] as const;\r\nexport type RecorderState = (typeof recorderStates)[number];\r\n\r\nconst inactiveStates = ['Init', 'Finished', 'Error', 'Canceled'] as const satisfies Readonly<RecorderState[]>;\r\ntype InactiveState = (typeof inactiveStates)[number];\r\n\r\nconst activeStates = [\r\n  'RequestingMedia',\r\n  'OpeningWebSocket',\r\n  'Running',\r\n  'FinishingProcessing',\r\n] as const satisfies Readonly<RecorderState[]>;\r\ntype ActiveState = (typeof activeStates)[number];\r\n\r\nconst websocketStates = ['OpeningWebSocket', 'Running', 'FinishingProcessing'] as const satisfies Readonly<\r\n  RecorderState[]\r\n>;\r\ntype WebSocketState = (typeof websocketStates)[number];\r\n\r\nexport function isInactiveState(state: RecorderState): state is InactiveState {\r\n  return inactiveStates.includes(state as InactiveState);\r\n}\r\n\r\nexport function isActiveState(state: RecorderState): state is ActiveState {\r\n  return activeStates.includes(state as ActiveState);\r\n}\r\n\r\nexport function isWebSocketState(state: RecorderState): state is WebSocketState {\r\n  return websocketStates.includes(state as WebSocketState);\r\n}"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;AAC7D,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGD,MAAM,iBAAiB;IAAC;IAAQ;IAAY;IAAS;CAAW;AAGhE,MAAM,eAAe;IACnB;IACA;IACA;IACA;CACD;AAGD,MAAM,kBAAkB;IAAC;IAAoB;IAAW;CAAsB;AAKvE,SAAS,gBAAgB,KAAoB;IAClD,OAAO,eAAe,QAAQ,CAAC;AACjC;AAEO,SAAS,cAAc,KAAoB;IAChD,OAAO,aAAa,QAAQ,CAAC;AAC/B;AAEO,SAAS,iBAAiB,KAAoB;IACnD,OAAO,gBAAgB,QAAQ,CAAC;AAClC", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hackathon/vtb-hack/arc/frontend/src/lib/ario/sst-client.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { ErrorStatus } from './errors';\r\nimport { isActiveState, isInactiveState, isWebSocketState, RecorderState } from './state';\r\nimport { SpeechToTextAPIRequest, SpeechToTextAPIResponse, TranslationConfig } from './types';\r\n\r\nconst defaultWebsocketUri = 'wss://stt-rt.soniox.com/transcribe-websocket';\r\n\r\nconst defaultBufferQueueSize = 1000;\r\n\r\nconst recorderTimeSliceMs = 120;\r\n\r\nconst finalizeMessage = '{ \"type\": \"finalize\" }';\r\n\r\ntype ApiKeyGetter = () => Promise<string>;\r\n\r\ntype Callbacks = {\r\n  onStateChange?: (update: { oldState: RecorderState; newState: RecorderState }) => void;\r\n  onStarted?: () => void;\r\n  onPartialResult?: (result: SpeechToTextAPIResponse) => void;\r\n  onFinished?: () => void;\r\n  /**\r\n   * Called when an error occurs.\r\n   *\r\n   * @param status - The error status.\r\n   * @param message - More descriptive error message.\r\n   * @param errorCode - The error code. Returned only if status is `api_error`.\r\n   */\r\n  onError?: (status: ErrorStatus, message: string, errorCode: number | undefined) => void;\r\n};\r\n\r\ntype SonioxClientOptions = {\r\n  /**\r\n   * WebSocket URI. If not provided, the default URI will be used.\r\n   */\r\n  webSocketUri?: string;\r\n\r\n  /**\r\n   * Either a string or a an async function which returns api key.\r\n   * Function can be used to generate a temporary API key, which is useful if you want to avoid exposing your API key to the client.\r\n   */\r\n  apiKey: string | ApiKeyGetter;\r\n\r\n  /**\r\n   * How many messages to queue before websocket is opened. If full, error will be thrown.\r\n   * (opening websocket might take some time, especially if the API key fetching is slow or\r\n   * the user has a slow connection)\r\n   */\r\n  bufferQueueSize?: number;\r\n} & Callbacks;\r\n\r\ntype AudioOptions = {\r\n  /**\r\n   * One of the available Speech-to-Text models.\r\n   */\r\n  model: string;\r\n\r\n  // Transcription options\r\n  /**\r\n   * List of language codes to hint the API on what language to transcribe.\r\n   *\r\n   * Example: `['en', 'fr', 'de']`\r\n   */\r\n  languageHints?: string[];\r\n\r\n  /**\r\n   * Context string to pass to the API.\r\n   */\r\n  context?: string;\r\n\r\n  /**\r\n   * When true, speakers are identified and separated in the transcription output.\r\n   */\r\n  enableSpeakerDiarization?: boolean;\r\n\r\n  /** When true, language identification is enabled. */\r\n  enableLanguageIdentification?: boolean;\r\n\r\n  /** When true, endpoint detection is enabled. */\r\n  enableEndpointDetection?: boolean;\r\n\r\n  /**\r\n   * Translation configuration. Can be one-way or two-way translation.\r\n   */\r\n  translation?: TranslationConfig;\r\n\r\n  /**\r\n   * The format of the streamed audio (e.g., \"auto\", \"s16le\").\r\n   */\r\n  audioFormat?: string;\r\n\r\n  /**\r\n   * Required for raw PCM formats.\r\n   */\r\n  sampleRate?: number;\r\n\r\n  /**\r\n   * Required for raw PCM formats. Typically 1 for mono audio, 2 for stereo.\r\n   */\r\n  numChannels?: number;\r\n\r\n  /**\r\n   * A client-defined identifier to track this stream. Can be any string. If not provided, it will be auto-generated.\r\n   */\r\n  clientReferenceId?: string;\r\n\r\n  /**\r\n   * Can be used to set the `echoCancellation` and `noiseSuppression` properties of the MediaTrackConstraints object.\r\n   * See https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints for more details.\r\n   */\r\n  audioConstraints?: MediaTrackConstraints;\r\n\r\n  /**\r\n   * MediaRecorder options: https://developer.mozilla.org/en-US/docs/Web/API/MediaRecorder/MediaRecorder\r\n   */\r\n  mediaRecorderOptions?: Record<string, any>;\r\n\r\n  /**\r\n   * If you don't want to transcribe audio from microphone, you can pass a MediaStream to the `stream` option.\r\n   * This can be useful if you want to transcribe audio from a file or a custom source.\r\n   */\r\n  stream?: MediaStream;\r\n} & Callbacks;\r\n\r\nconst getDefaultSonioxClientOptions = (): SonioxClientOptions => ({\r\n  apiKey: '',\r\n  bufferQueueSize: defaultBufferQueueSize,\r\n});\r\n\r\nconst defaultAudioConstraints: MediaTrackConstraints = {\r\n  echoCancellation: false,\r\n  noiseSuppression: false,\r\n  autoGainControl: false,\r\n  channelCount: 1,\r\n  sampleRate: 44100,\r\n};\r\n\r\nexport class SonioxClient {\r\n  static isSupported = Boolean('WebSocket' in window && navigator.mediaDevices && navigator.mediaDevices.getUserMedia);\r\n\r\n  _state: RecorderState = 'Init';\r\n  _options: SonioxClientOptions;\r\n  _audioOptions: AudioOptions | null;\r\n  _websocket: WebSocket | null;\r\n  _mediaRecorder: MediaRecorder | null;\r\n  _queuedMessages: (Blob | string)[] = []; // Queued data (before websocket is opened)\r\n\r\n  /**\r\n   * SonioxClient connects to the Soniox Speech-to-Text API for real-time speech-to-text transcription and translation.\r\n   * It provides a simple API for starting and stopping the transcription, as well as handling the transcription results.\r\n   *\r\n   * @example\r\n   * const sonioxClient = new SonioxClient({\r\n   *   apiKey: '<SONIOX_API_KEY>',\r\n   *   onPartialResult: (result) => {\r\n   *     console.log('partial result', result.text);\r\n   *   },\r\n   * });\r\n   * sonioxClient.start();\r\n   */\r\n  constructor(options?: SonioxClientOptions) {\r\n    if (!SonioxClient.isSupported) {\r\n      throw 'airo Speech-to-Text is not supported on this browser.';\r\n    }\r\n\r\n    this._options = {\r\n      ...getDefaultSonioxClientOptions(),\r\n      ...options,\r\n    };\r\n\r\n    this._audioOptions = null;\r\n\r\n    this._websocket = null;\r\n    this._mediaRecorder = null;\r\n  }\r\n\r\n  _hasCallback = <T extends keyof Callbacks>(name: T): boolean => {\r\n    return this._options[name] != null || this._audioOptions?.[name] != null;\r\n  };\r\n\r\n  _callback = <T extends keyof Callbacks>(name: T, ...args: Parameters<NonNullable<Callbacks[T]>>): void => {\r\n    // @ts-ignore\r\n    this._options[name]?.(...args);\r\n    // @ts-ignore\r\n    this._audioOptions?.[name]?.(...args);\r\n  };\r\n\r\n  _setState(newState: RecorderState): void {\r\n    const oldState = this._state;\r\n    this._state = newState;\r\n    this._callback('onStateChange', {\r\n      oldState,\r\n      newState,\r\n    });\r\n  }\r\n\r\n  get state(): RecorderState {\r\n    return this._state;\r\n  }\r\n\r\n  /**\r\n   * Start transcription. You can pass options to configure the transcription settings, source and callbacks.\r\n   */\r\n  start = async (audioOptions: AudioOptions): Promise<void> => {\r\n    if (isActiveState(this._state)) {\r\n      throw new Error('SonioxClient is already active');\r\n    }\r\n\r\n    this._audioOptions = { ...audioOptions };\r\n\r\n    let stream: MediaStream | undefined = undefined;\r\n\r\n    if (audioOptions.stream != null) {\r\n      // User has provided a stream, don't request microphone access, simply transcribe the stream.\r\n      // We need to clone the stream so internal stopping doesn't affect the user's stream.\r\n      stream = audioOptions.stream.clone();\r\n    } else {\r\n      // Stream not given, request stream from microphone.\r\n      this._setState('RequestingMedia');\r\n\r\n      try {\r\n        // Request microphone access and get stream\r\n        stream = await navigator.mediaDevices.getUserMedia({\r\n          audio: this._audioOptions.audioConstraints ? this._audioOptions.audioConstraints : defaultAudioConstraints,\r\n        });\r\n      } catch (e) {\r\n        this._onError('get_user_media_failed', e?.toString());\r\n      }\r\n    }\r\n\r\n    // Mostly here to make typescript happy\r\n    if (stream == null) {\r\n      throw new Error('Failed to create stream');\r\n    }\r\n\r\n    // New media stream\r\n    this._mediaRecorder = new MediaRecorder(\r\n      stream,\r\n      audioOptions.mediaRecorderOptions ? audioOptions.mediaRecorderOptions : {},\r\n    );\r\n\r\n    // Start collecting data\r\n    this._queuedMessages = [];\r\n    this._mediaRecorder.addEventListener('dataavailable', this._onMediaRecorderData);\r\n    this._mediaRecorder.addEventListener('error', this._onMediaRecorderError);\r\n    this._mediaRecorder.addEventListener('pause', this._onMediaRecorderPause);\r\n    this._mediaRecorder.addEventListener('stop', this._onMediaRecorderStop);\r\n\r\n    // Start recording\r\n    this._mediaRecorder.start(recorderTimeSliceMs);\r\n\r\n    // Open websocket\r\n    this._setState('OpeningWebSocket');\r\n    this._websocket = new WebSocket(this._options.webSocketUri ?? defaultWebsocketUri);\r\n\r\n    this._websocket.addEventListener('open', this._onWebSocketOpen);\r\n    this._websocket.addEventListener('error', this._onWebSocketError);\r\n    this._websocket.addEventListener('message', this._onWebSocketMessage);\r\n  };\r\n\r\n  /**\r\n   * Stop transcription. Stopping transcription will send stop signal to the API and wait for the final results to be received.\r\n   * Only after the final results are received, the transcription will be finished. If you want to cancel the transcription immediately,\r\n   * (for example, on component unmount), you should probably use the `cancel()` method instead.\r\n   */\r\n  stop = (): void => {\r\n    if (this._state == 'RequestingMedia' || this._state == 'OpeningWebSocket') {\r\n      this._closeResources();\r\n      this._handleFinished();\r\n    } else if (this._state == 'Running') {\r\n      // Finished recording, waiting for last events from api\r\n      this._setState('FinishingProcessing');\r\n      this._closeSource();\r\n      // Send empty message to api to indicate that we're done\r\n      this._websocket?.send('');\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Cancel transcription. Cancelling transcription will stop the transcription immediately and close the resources.\r\n   * For user initiated cancellation, you should probably use the `stop()` method instead.\r\n   */\r\n  cancel = (): void => {\r\n    if (!isInactiveState(this._state)) {\r\n      this._closeResources();\r\n      this._setState('Canceled');\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Trigger finalize. This will finalize all non-final tokens.\r\n   */\r\n  finalize = (): void => {\r\n    if (this._state == 'RequestingMedia' || this._state == 'OpeningWebSocket') {\r\n      // Still waiting for websocket to open, queue the event\r\n      if (this._queuedMessages.length < (this._options.bufferQueueSize ?? defaultBufferQueueSize)) {\r\n        this._queuedMessages.push(finalizeMessage);\r\n      } else {\r\n        this._onError('queue_limit_exceeded', 'Queue size exceeded before websocket connection was established.');\r\n      }\r\n    } else if (this._state == 'Running' || this._state == 'FinishingProcessing') {\r\n      this._websocket?.send(finalizeMessage);\r\n    }\r\n  };\r\n\r\n  // Media recorder events\r\n\r\n  _onMediaRecorderData = async (event: BlobEvent): Promise<void> => {\r\n    if (this._state === 'OpeningWebSocket') {\r\n      // Still waiting for websocket to open, queue the event\r\n      if (this._queuedMessages.length < (this._options.bufferQueueSize ?? defaultBufferQueueSize)) {\r\n        this._queuedMessages.push(event.data);\r\n      } else {\r\n        this._onError('queue_limit_exceeded', 'Queue size exceeded before websocket connection was established.');\r\n      }\r\n    } else if (this._state === 'Running') {\r\n      const data = await event.data.arrayBuffer();\r\n      this._websocket?.send(data);\r\n    }\r\n  };\r\n\r\n  _onMediaRecorderError = (event: Event): void => {\r\n    this._onError('media_recorder_error', (event as ErrorEvent).error ?? 'Unknown error');\r\n  };\r\n\r\n  _onMediaRecorderPause = (_event: Event): void => {\r\n    this.stop();\r\n  };\r\n\r\n  _onMediaRecorderStop = (_event: Event): void => {\r\n    this.stop();\r\n  };\r\n\r\n  // Websocket events\r\n\r\n  _onWebSocketOpen = (event: Event): void => {\r\n    void this._onWebSocketOpenAsync(event);\r\n  };\r\n\r\n  _onWebSocketOpenAsync = async (_event: Event): Promise<void> => {\r\n    if (this._state !== 'OpeningWebSocket' || this._audioOptions == null) {\r\n      return;\r\n    }\r\n\r\n    const opts = this._audioOptions;\r\n\r\n    // If api key is getter, call it to get the key, if not, use the key directly\r\n    let apiKey: string;\r\n    if (typeof this._options.apiKey === 'function') {\r\n      try {\r\n        apiKey = await this._options.apiKey();\r\n      } catch (e) {\r\n        this._onError('api_key_fetch_failed', e?.toString());\r\n        return;\r\n      }\r\n    } else {\r\n      apiKey = this._options.apiKey;\r\n    }\r\n\r\n    // Check state again (might be different if state changed during api key fetching)\r\n    if (this._state !== 'OpeningWebSocket') {\r\n      return;\r\n    }\r\n\r\n    const request: SpeechToTextAPIRequest = {\r\n      api_key: apiKey,\r\n      model: opts.model,\r\n      audio_format: opts.audioFormat ? opts.audioFormat : 'auto',\r\n      sample_rate: opts.sampleRate,\r\n      num_channels: opts.numChannels,\r\n      language_hints: opts.languageHints,\r\n      context: opts.context,\r\n      enable_speaker_diarization: opts.enableSpeakerDiarization,\r\n      enable_language_identification: opts.enableLanguageIdentification,\r\n      enable_endpoint_detection: opts.enableEndpointDetection,\r\n      translation: opts.translation,\r\n      client_reference_id: opts.clientReferenceId,\r\n    };\r\n\r\n    // Send initial request\r\n    this._websocket?.send(JSON.stringify(request));\r\n\r\n    // Send all queued messages (if any)\r\n    for (const message of this._queuedMessages) {\r\n      this._websocket?.send(message);\r\n    }\r\n    this._queuedMessages = [];\r\n\r\n    this._setState('Running');\r\n    this._callback('onStarted');\r\n  };\r\n\r\n  _onWebSocketError = (_event: Event): void => {\r\n    if (!isWebSocketState(this._state)) {\r\n      return;\r\n    }\r\n    this._onError('websocket_error', 'WebSocket error occurred.');\r\n  };\r\n\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  _onWebSocketMessage = (event: MessageEvent<any>): void => {\r\n    if ((this._state != 'Running' && this._state != 'FinishingProcessing') || this._audioOptions == null) {\r\n      return;\r\n    }\r\n    const response = JSON.parse(event.data) as SpeechToTextAPIResponse;\r\n\r\n    if (response.error_code != null || response.error_message != null) {\r\n      this._onError('api_error', response.error_message, response.error_code);\r\n      return;\r\n    }\r\n\r\n    this._callback('onPartialResult', response);\r\n\r\n    if (response.finished) {\r\n      this._handleFinished();\r\n    }\r\n  };\r\n\r\n  _onError = (status: ErrorStatus, message: string | undefined, errorCode: number | undefined = undefined): void => {\r\n    this._setState('Error');\r\n    this._closeResources();\r\n\r\n    if (this._hasCallback('onError')) {\r\n      this._callback('onError', status, message ?? 'Unknown error', errorCode);\r\n    } else {\r\n      throw new Error(`SonioxClient error: ${status}: ${message ?? 'Unknown error'}`);\r\n    }\r\n  };\r\n\r\n  _closeSource = (): void => {\r\n    // Close media recorder\r\n    if (this._mediaRecorder != null) {\r\n      this._mediaRecorder.removeEventListener('dataavailable', this._onMediaRecorderData);\r\n      this._mediaRecorder.removeEventListener('error', this._onMediaRecorderError);\r\n      this._mediaRecorder.removeEventListener('pause', this._onMediaRecorderPause);\r\n      this._mediaRecorder.removeEventListener('stop', this._onMediaRecorderStop);\r\n\r\n      this._mediaRecorder.stop();\r\n      this._mediaRecorder.stream.getTracks().forEach((track) => track.stop());\r\n      this._mediaRecorder = null;\r\n    }\r\n  };\r\n\r\n  _closeResources = (): void => {\r\n    this._queuedMessages = [];\r\n\r\n    // Close websocket\r\n    if (this._websocket != null) {\r\n      this._websocket.removeEventListener('open', this._onWebSocketOpen);\r\n      this._websocket.removeEventListener('error', this._onWebSocketError);\r\n      this._websocket.removeEventListener('message', this._onWebSocketMessage);\r\n      this._websocket.close();\r\n      this._websocket = null;\r\n    }\r\n\r\n    this._closeSource();\r\n  };\r\n\r\n  _handleFinished(): void {\r\n    this._closeResources();\r\n    this._setState('Finished');\r\n    this._callback('onFinished');\r\n  }\r\n}\r\n\r\n/**\r\n * @deprecated Use SonioxClient instead.\r\n *\r\n */\r\nexport const RecordTranscribe = SonioxClient;"], "names": [], "mappings": "AAAA,oDAAoD;;;;;;;AAEpD;;;AAGA,MAAM,sBAAsB;AAE5B,MAAM,yBAAyB;AAE/B,MAAM,sBAAsB;AAE5B,MAAM,kBAAkB;AAgHxB,MAAM,gCAAgC,IAA2B,CAAC;QAChE,QAAQ;QACR,iBAAiB;IACnB,CAAC;AAED,MAAM,0BAAiD;IACrD,kBAAkB;IAClB,kBAAkB;IAClB,iBAAiB;IACjB,cAAc;IACd,YAAY;AACd;AAEO,MAAM;IAkDX,UAAU,QAAuB,EAAQ;QACvC,MAAM,WAAW,IAAI,CAAC,MAAM;QAC5B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,CAAC,iBAAiB;YAC9B;YACA;QACF;IACF;IAEA,IAAI,QAAuB;QACzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAoQA,kBAAwB;QACtB,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,SAAS,CAAC;QACf,IAAI,CAAC,SAAS,CAAC;IACjB;IA3TA;;;;;;;;;;;;GAYC,GACD,YAAY,OAA6B,CAAE;;QApB3C,+KAAA,UAAwB;QACxB,+KAAA,YAAA,KAAA;QACA,+KAAA,iBAAA,KAAA;QACA,+KAAA,cAAA,KAAA;QACA,+KAAA,kBAAA,KAAA;QACA,+KAAA,mBAAqC,EAAE,GAAE,2CAA2C;QA+BpF,+KAAA,gBAAe,CAA4B;gBACH;YAAtC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,EAAA,sBAAA,IAAI,CAAC,aAAa,cAAlB,0CAAA,mBAAoB,CAAC,KAAK,KAAI;QACtE;QAEA,+KAAA,aAAY,SAA4B;6CAAY;gBAAA;;gBAClD,aAAa;YACb,qBAAA,gBACA,aAAa;YACb,0BAAA;aAFA,sBAAA,CAAA,iBAAA,MAAK,QAAQ,CAAA,CAAC,KAAK,cAAnB,0CAAA,yBAAA,mBAAyB;aAEzB,sBAAA,MAAK,aAAa,cAAlB,2CAAA,2BAAA,mBAAoB,CAAC,KAAK,cAA1B,+CAAA,8BAAA,wBAAgC;QAClC;QAeA;;GAEC,GACD,+KAAA,SAAQ,OAAO;YACb,IAAI,IAAA,+IAAa,EAAC,IAAI,CAAC,MAAM,GAAG;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,aAAa,GAAG;gBAAE,GAAG,YAAY;YAAC;YAEvC,IAAI,SAAkC;YAEtC,IAAI,aAAa,MAAM,IAAI,MAAM;gBAC/B,6FAA6F;gBAC7F,qFAAqF;gBACrF,SAAS,aAAa,MAAM,CAAC,KAAK;YACpC,OAAO;gBACL,oDAAoD;gBACpD,IAAI,CAAC,SAAS,CAAC;gBAEf,IAAI;oBACF,2CAA2C;oBAC3C,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;wBACjD,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG;oBACrF;gBACF,EAAE,OAAO,GAAG;oBACV,IAAI,CAAC,QAAQ,CAAC,yBAAyB,cAAA,wBAAA,EAAG,QAAQ;gBACpD;YACF;YAEA,uCAAuC;YACvC,IAAI,UAAU,MAAM;gBAClB,MAAM,IAAI,MAAM;YAClB;YAEA,mBAAmB;YACnB,IAAI,CAAC,cAAc,GAAG,IAAI,cACxB,QACA,aAAa,oBAAoB,GAAG,aAAa,oBAAoB,GAAG,CAAC;YAG3E,wBAAwB;YACxB,IAAI,CAAC,eAAe,GAAG,EAAE;YACzB,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,iBAAiB,IAAI,CAAC,oBAAoB;YAC/E,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,IAAI,CAAC,qBAAqB;YACxE,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,IAAI,CAAC,qBAAqB;YACxE,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,IAAI,CAAC,oBAAoB;YAEtE,kBAAkB;YAClB,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;YAE1B,iBAAiB;YACjB,IAAI,CAAC,SAAS,CAAC;gBACiB;YAAhC,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAA,8BAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,cAA1B,yCAAA,8BAA8B;YAE9D,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,IAAI,CAAC,gBAAgB;YAC9D,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,IAAI,CAAC,iBAAiB;YAChE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,WAAW,IAAI,CAAC,mBAAmB;QACtE;QAEA;;;;GAIC,GACD,+KAAA,QAAO;YACL,IAAI,IAAI,CAAC,MAAM,IAAI,qBAAqB,IAAI,CAAC,MAAM,IAAI,oBAAoB;gBACzE,IAAI,CAAC,eAAe;gBACpB,IAAI,CAAC,eAAe;YACtB,OAAO,IAAI,IAAI,CAAC,MAAM,IAAI,WAAW;oBAInC,wDAAwD;gBACxD;gBAJA,uDAAuD;gBACvD,IAAI,CAAC,SAAS,CAAC;gBACf,IAAI,CAAC,YAAY;iBAEjB,mBAAA,IAAI,CAAC,UAAU,cAAf,uCAAA,iBAAiB,IAAI,CAAC;YACxB;QACF;QAEA;;;GAGC,GACD,+KAAA,UAAS;YACP,IAAI,CAAC,IAAA,iJAAe,EAAC,IAAI,CAAC,MAAM,GAAG;gBACjC,IAAI,CAAC,eAAe;gBACpB,IAAI,CAAC,SAAS,CAAC;YACjB;QACF;QAEA;;GAEC,GACD,+KAAA,YAAW;YACT,IAAI,IAAI,CAAC,MAAM,IAAI,qBAAqB,IAAI,CAAC,MAAM,IAAI,oBAAoB;oBAEtC;gBADnC,uDAAuD;gBACvD,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAA,iCAAA,IAAI,CAAC,QAAQ,CAAC,eAAe,cAA7B,4CAAA,iCAAiC,sBAAsB,GAAG;oBAC3F,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;gBAC5B,OAAO;oBACL,IAAI,CAAC,QAAQ,CAAC,wBAAwB;gBACxC;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,IAAI,aAAa,IAAI,CAAC,MAAM,IAAI,uBAAuB;oBAC3E;iBAAA,mBAAA,IAAI,CAAC,UAAU,cAAf,uCAAA,iBAAiB,IAAI,CAAC;YACxB;QACF;QAEA,wBAAwB;QAExB,+KAAA,wBAAuB,OAAO;YAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,oBAAoB;oBAEH;gBADnC,uDAAuD;gBACvD,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAA,iCAAA,IAAI,CAAC,QAAQ,CAAC,eAAe,cAA7B,4CAAA,iCAAiC,sBAAsB,GAAG;oBAC3F,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI;gBACtC,OAAO;oBACL,IAAI,CAAC,QAAQ,CAAC,wBAAwB;gBACxC;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW;oBAEpC;gBADA,MAAM,OAAO,MAAM,MAAM,IAAI,CAAC,WAAW;iBACzC,mBAAA,IAAI,CAAC,UAAU,cAAf,uCAAA,iBAAiB,IAAI,CAAC;YACxB;QACF;QAEA,+KAAA,yBAAwB,CAAC;gBACe;YAAtC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAA,SAAA,AAAC,MAAqB,KAAK,cAA3B,oBAAA,SAA+B;QACvE;QAEA,+KAAA,yBAAwB,CAAC;YACvB,IAAI,CAAC,IAAI;QACX;QAEA,+KAAA,wBAAuB,CAAC;YACtB,IAAI,CAAC,IAAI;QACX;QAEA,mBAAmB;QAEnB,+KAAA,oBAAmB,CAAC;YAClB,KAAK,IAAI,CAAC,qBAAqB,CAAC;QAClC;QAEA,+KAAA,yBAAwB,OAAO;gBAwC7B,uBAAuB;YACvB;YAxCA,IAAI,IAAI,CAAC,MAAM,KAAK,sBAAsB,IAAI,CAAC,aAAa,IAAI,MAAM;gBACpE;YACF;YAEA,MAAM,OAAO,IAAI,CAAC,aAAa;YAE/B,6EAA6E;YAC7E,IAAI;YACJ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,YAAY;gBAC9C,IAAI;oBACF,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM;gBACrC,EAAE,OAAO,GAAG;oBACV,IAAI,CAAC,QAAQ,CAAC,wBAAwB,cAAA,wBAAA,EAAG,QAAQ;oBACjD;gBACF;YACF,OAAO;gBACL,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;YAC/B;YAEA,kFAAkF;YAClF,IAAI,IAAI,CAAC,MAAM,KAAK,oBAAoB;gBACtC;YACF;YAEA,MAAM,UAAkC;gBACtC,SAAS;gBACT,OAAO,KAAK,KAAK;gBACjB,cAAc,KAAK,WAAW,GAAG,KAAK,WAAW,GAAG;gBACpD,aAAa,KAAK,UAAU;gBAC5B,cAAc,KAAK,WAAW;gBAC9B,gBAAgB,KAAK,aAAa;gBAClC,SAAS,KAAK,OAAO;gBACrB,4BAA4B,KAAK,wBAAwB;gBACzD,gCAAgC,KAAK,4BAA4B;gBACjE,2BAA2B,KAAK,uBAAuB;gBACvD,aAAa,KAAK,WAAW;gBAC7B,qBAAqB,KAAK,iBAAiB;YAC7C;aAGA,mBAAA,IAAI,CAAC,UAAU,cAAf,uCAAA,iBAAiB,IAAI,CAAC,KAAK,SAAS,CAAC;YAErC,oCAAoC;YACpC,KAAK,MAAM,WAAW,IAAI,CAAC,eAAe,CAAE;oBAC1C;iBAAA,oBAAA,IAAI,CAAC,UAAU,cAAf,wCAAA,kBAAiB,IAAI,CAAC;YACxB;YACA,IAAI,CAAC,eAAe,GAAG,EAAE;YAEzB,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,SAAS,CAAC;QACjB;QAEA,+KAAA,qBAAoB,CAAC;YACnB,IAAI,CAAC,IAAA,kJAAgB,EAAC,IAAI,CAAC,MAAM,GAAG;gBAClC;YACF;YACA,IAAI,CAAC,QAAQ,CAAC,mBAAmB;QACnC;QAEA,8DAA8D;QAC9D,+KAAA,uBAAsB,CAAC;YACrB,IAAI,AAAC,IAAI,CAAC,MAAM,IAAI,aAAa,IAAI,CAAC,MAAM,IAAI,yBAA0B,IAAI,CAAC,aAAa,IAAI,MAAM;gBACpG;YACF;YACA,MAAM,WAAW,KAAK,KAAK,CAAC,MAAM,IAAI;YAEtC,IAAI,SAAS,UAAU,IAAI,QAAQ,SAAS,aAAa,IAAI,MAAM;gBACjE,IAAI,CAAC,QAAQ,CAAC,aAAa,SAAS,aAAa,EAAE,SAAS,UAAU;gBACtE;YACF;YAEA,IAAI,CAAC,SAAS,CAAC,mBAAmB;YAElC,IAAI,SAAS,QAAQ,EAAE;gBACrB,IAAI,CAAC,eAAe;YACtB;QACF;QAEA,+KAAA,YAAW,SAAC,QAAqB;gBAA6B,6EAAgC;YAC5F,MAAK,SAAS,CAAC;YACf,MAAK,eAAe;YAEpB,IAAI,MAAK,YAAY,CAAC,YAAY;gBAChC,MAAK,SAAS,CAAC,WAAW,QAAQ,oBAAA,qBAAA,UAAW,iBAAiB;YAChE,OAAO;gBACL,MAAM,IAAI,MAAM,AAAC,uBAAiC,OAAX,QAAO,MAA+B,OAA3B,oBAAA,qBAAA,UAAW;YAC/D;QACF;QAEA,+KAAA,gBAAe;YACb,uBAAuB;YACvB,IAAI,IAAI,CAAC,cAAc,IAAI,MAAM;gBAC/B,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,iBAAiB,IAAI,CAAC,oBAAoB;gBAClF,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,IAAI,CAAC,qBAAqB;gBAC3E,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,IAAI,CAAC,qBAAqB;gBAC3E,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,QAAQ,IAAI,CAAC,oBAAoB;gBAEzE,IAAI,CAAC,cAAc,CAAC,IAAI;gBACxB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC,QAAU,MAAM,IAAI;gBACpE,IAAI,CAAC,cAAc,GAAG;YACxB;QACF;QAEA,+KAAA,mBAAkB;YAChB,IAAI,CAAC,eAAe,GAAG,EAAE;YAEzB,kBAAkB;YAClB,IAAI,IAAI,CAAC,UAAU,IAAI,MAAM;gBAC3B,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,QAAQ,IAAI,CAAC,gBAAgB;gBACjE,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,SAAS,IAAI,CAAC,iBAAiB;gBACnE,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,WAAW,IAAI,CAAC,mBAAmB;gBACvE,IAAI,CAAC,UAAU,CAAC,KAAK;gBACrB,IAAI,CAAC,UAAU,GAAG;YACpB;YAEA,IAAI,CAAC,YAAY;QACnB;QAvSE,IAAI,CAAC,aAAa,WAAW,EAAE;YAC7B,MAAM;QACR;QAEA,IAAI,CAAC,QAAQ,GAAG;YACd,GAAG,+BAA+B;YAClC,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,aAAa,GAAG;QAErB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,cAAc,GAAG;IACxB;AAiSF;AArUE,yKADW,cACJ,eAAc,QAAQ,eAAe,UAAU,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,YAAY;AA2U9G,MAAM,mBAAmB", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hackathon/vtb-hack/arc/frontend/src/lib/ario/index.ts"], "sourcesContent": ["export * from './errors';\r\nexport * from './sst-client';\r\nexport * from './state';\r\nexport * from './types';"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hackathon/vtb-hack/arc/frontend/src/hooks/use-transcribe.ts"], "sourcesContent": ["import {\r\n    SonioxClient,\r\n    type ErrorStatus,\r\n    type RecorderState,\r\n    type Token,\r\n    type TranslationConfig,\r\n} from '@/lib/ario';\r\nimport { useCallback, useEffect, useRef, useState } from 'react';\r\n\r\nconst END_TOKEN = '<end>';\r\n\r\ninterface UseTranscribeParameters {\r\n    apiKey: string | (() => Promise<string>);\r\n    translationConfig?: TranslationConfig;\r\n    onStarted?: () => void;\r\n    onFinished?: () => void;\r\n    onEndOfSpeech?: (fullText: string) => void;\r\n    onPartialResult?: (finalTokens: Token[], nonFinalTokens: Token[]) => void;\r\n}\r\n\r\ntype TranscriptionError = {\r\n    status: ErrorStatus;\r\n    message: string;\r\n    errorCode: number | undefined;\r\n};\r\n\r\n// useTranscribe hook wraps Soniox speech-to-text-web SDK.\r\nexport default function useTranscribe({ apiKey, translationConfig, onStarted, onFinished, onEndOfSpeech, onPartialResult }: UseTranscribeParameters) {\r\n    const sonioxClient = useRef<SonioxClient | null>(null);\r\n\r\n    if (sonioxClient.current == null) {\r\n        sonioxClient.current = new SonioxClient({\r\n            apiKey: apiKey,\r\n        });\r\n    }\r\n\r\n    const [state, setState] = useState<RecorderState>('Init');\r\n    const [finalTokens, setFinalTokens] = useState<Token[]>([]);\r\n    const [nonFinalTokens, setNonFinalTokens] = useState<Token[]>([]);\r\n    const [error, setError] = useState<TranscriptionError | null>(null);\r\n\r\n    const startTranscription = useCallback(async () => {\r\n        setFinalTokens([]);\r\n        setNonFinalTokens([]);\r\n        setError(null);\r\n\r\n        let tempFinalTokens: Token[] = [];\r\n\r\n        // First message we send contains configuration. Here we set if we set if we\r\n        // are transcribing or translating. For translation we also set if it is\r\n        // one-way or two-way.\r\n        sonioxClient.current?.start({\r\n            model: 'stt-rt-preview',\r\n            enableLanguageIdentification: true,\r\n            enableSpeakerDiarization: true,\r\n            enableEndpointDetection: true,\r\n            translation: translationConfig || undefined,\r\n\r\n            onFinished: onFinished,\r\n            onStarted: onStarted,\r\n\r\n            onError: (status: ErrorStatus, message: string, errorCode: number | undefined) => {\r\n                setError({ status, message, errorCode });\r\n            },\r\n\r\n            onStateChange: ({ newState }) => {\r\n                setState(newState);\r\n            },\r\n\r\n            // When we receive some tokens back, sort them based on their status --\r\n            // is it final or non-final token.\r\n            onPartialResult(result) {\r\n                // console.log('Partial result', result);\r\n                const newFinalTokens: Token[] = [];\r\n                const newNonFinalTokens: Token[] = [];\r\n                let endTokenReceived = false;\r\n\r\n                for (const token of result.tokens) {\r\n                    // Ignore endpoint detection tokens\r\n                    if (token.text === END_TOKEN) {\r\n                        endTokenReceived = true;\r\n                        continue;\r\n                    }\r\n\r\n                    if (token.is_final) {\r\n                        newFinalTokens.push(token);\r\n                    } else {\r\n                        newNonFinalTokens.push(token);\r\n                    }\r\n                }\r\n\r\n                const currentFinalTokens = [...tempFinalTokens, ...newFinalTokens];\r\n                tempFinalTokens = currentFinalTokens;\r\n\r\n                if (onPartialResult) {\r\n                    onPartialResult(currentFinalTokens, newNonFinalTokens);\r\n                }\r\n\r\n                if (endTokenReceived) {\r\n                    const fullText = currentFinalTokens.map((t) => t.text).join('');\r\n                    if (fullText.trim() && onEndOfSpeech) {\r\n                        onEndOfSpeech(fullText);\r\n                    }\r\n                    tempFinalTokens = [];\r\n                    setNonFinalTokens([]);\r\n                    setNonFinalTokens([]);\r\n                } else {\r\n                    setFinalTokens(currentFinalTokens);\r\n                }\r\n\r\n                setNonFinalTokens(newNonFinalTokens);\r\n            },\r\n        });\r\n    }, [onFinished, onStarted, translationConfig, onEndOfSpeech, finalTokens]);\r\n\r\n    const stopTranscription = useCallback(() => {\r\n        sonioxClient.current?.stop();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        return () => {\r\n            sonioxClient.current?.cancel();\r\n        };\r\n    }, []);\r\n\r\n    return {\r\n        startTranscription,\r\n        stopTranscription,\r\n        state,\r\n        finalTokens,\r\n        nonFinalTokens,\r\n        error,\r\n    };\r\n}"], "names": [], "mappings": ";;;;AAAA;AAAA;AAOA;;;;AAEA,MAAM,YAAY;AAkBH,SAAS,cAAc,KAA6G;QAA7G,EAAE,MAAM,EAAE,iBAAiB,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,eAAe,EAA2B,GAA7G;;IAClC,MAAM,eAAe,IAAA,uKAAM,EAAsB;IAEjD,IAAI,aAAa,OAAO,IAAI,MAAM;QAC9B,aAAa,OAAO,GAAG,IAAI,sJAAY,CAAC;YACpC,QAAQ;QACZ;IACJ;IAEA,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAgB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAU,EAAE;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAU,EAAE;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAA4B;IAE9D,MAAM,qBAAqB,IAAA,4KAAW;yDAAC;gBAOnC,4EAA4E;YAC5E,wEAAwE;YACxE,sBAAsB;YACtB;YATA,eAAe,EAAE;YACjB,kBAAkB,EAAE;YACpB,SAAS;YAET,IAAI,kBAA2B,EAAE;aAKjC,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,KAAK,CAAC;gBACxB,OAAO;gBACP,8BAA8B;gBAC9B,0BAA0B;gBAC1B,yBAAyB;gBACzB,aAAa,qBAAqB;gBAElC,YAAY;gBACZ,WAAW;gBAEX,OAAO;qEAAE,CAAC,QAAqB,SAAiB;wBAC5C,SAAS;4BAAE;4BAAQ;4BAAS;wBAAU;oBAC1C;;gBAEA,aAAa;qEAAE;4BAAC,EAAE,QAAQ,EAAE;wBACxB,SAAS;oBACb;;gBAEA,uEAAuE;gBACvE,kCAAkC;gBAClC,iBAAgB,MAAM;oBAClB,yCAAyC;oBACzC,MAAM,iBAA0B,EAAE;oBAClC,MAAM,oBAA6B,EAAE;oBACrC,IAAI,mBAAmB;oBAEvB,KAAK,MAAM,SAAS,OAAO,MAAM,CAAE;wBAC/B,mCAAmC;wBACnC,IAAI,MAAM,IAAI,KAAK,WAAW;4BAC1B,mBAAmB;4BACnB;wBACJ;wBAEA,IAAI,MAAM,QAAQ,EAAE;4BAChB,eAAe,IAAI,CAAC;wBACxB,OAAO;4BACH,kBAAkB,IAAI,CAAC;wBAC3B;oBACJ;oBAEA,MAAM,qBAAqB;2BAAI;2BAAoB;qBAAe;oBAClE,kBAAkB;oBAElB,IAAI,iBAAiB;wBACjB,gBAAgB,oBAAoB;oBACxC;oBAEA,IAAI,kBAAkB;wBAClB,MAAM,WAAW,mBAAmB,GAAG;sFAAC,CAAC,IAAM,EAAE,IAAI;qFAAE,IAAI,CAAC;wBAC5D,IAAI,SAAS,IAAI,MAAM,eAAe;4BAClC,cAAc;wBAClB;wBACA,kBAAkB,EAAE;wBACpB,kBAAkB,EAAE;wBACpB,kBAAkB,EAAE;oBACxB,OAAO;wBACH,eAAe;oBACnB;oBAEA,kBAAkB;gBACtB;YACJ;QACJ;wDAAG;QAAC;QAAY;QAAW;QAAmB;QAAe;KAAY;IAEzE,MAAM,oBAAoB,IAAA,4KAAW;wDAAC;gBAClC;aAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,IAAI;QAC9B;uDAAG,EAAE;IAEL,IAAA,0KAAS;mCAAC;YACN;2CAAO;wBACH;qBAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,MAAM;gBAChC;;QACJ;kCAAG,EAAE;IAEL,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ;GA1GwB", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hackathon/vtb-hack/arc/frontend/src/components/call.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@/lib/utils'\r\nimport React, { memo, useCallback, useEffect, useRef } from 'react'\r\nimport Avatar from './avatar'\r\nimport { Button } from './ui/button';\r\nimport { MicIcon, MicOffIcon, PhoneIcon, PhoneOff, PhoneOffIcon } from 'lucide-react';\r\nimport useChat from '@/hooks/use-chat';\r\nimport { nanoid } from 'nanoid';\r\nimport useTranscribe from '@/hooks/use-transcribe';\r\n\r\nfunction AiCall() {\r\n\r\n  const { messages, addMessage, clearMessages, editMessage } = useChat();\r\n\r\n  const [joinedCall, setJoinedCall] = React.useState(false);\r\n  const joinedCallRef = useRef(joinedCall);\r\n  useEffect(() => {\r\n    joinedCallRef.current = joinedCall;\r\n  }, [joinedCall]);\r\n\r\n  const [isBotSpeaking, setIsBotSpeaking] = React.useState(false);\r\n  const [isUserSpeaking, setIsUserSpeaking] = React.useState(false);\r\n\r\n  const userMessageIdRef = useRef<string | null>(null);\r\n  const botMessageIdRef = useRef<string | null>(null);\r\n\r\n  const ttsWebSocketRef = useRef<WebSocket | null>(null);\r\n  const audioBufferQueueRef = useRef<ArrayBuffer[]>([]);\r\n  const isPlayingRef = useRef(false);\r\n\r\n  const [isMicOn, setIsMicOn] = React.useState(false);\r\n\r\n  function toggleJoinCall() {\r\n    if (!joinedCall === true) {\r\n      addMessage({\r\n        id: nanoid(8),\r\n        role: 'system',\r\n        content: 'You started the call'\r\n      })\r\n      startTranscription()\r\n    } else {\r\n      stopTranscription()\r\n      addMessage({\r\n        id: nanoid(8),\r\n        role: 'system',\r\n        content: 'You left the call'\r\n      })\r\n    }\r\n    setJoinedCall(!joinedCall);\r\n  }\r\n\r\n  useEffect(() => {\r\n    addMessage({\r\n      id: nanoid(8),\r\n      role: 'system',\r\n      content: 'You joined the room'\r\n    })\r\n  }, [])\r\n\r\n  const { state, finalTokens, nonFinalTokens, startTranscription, stopTranscription } = useTranscribe({\r\n    apiKey: \"4f7bdb6be03c50f473bf1bf90f929cf329f108447225452b5b831079581a66de\",\r\n    onEndOfSpeech: async (fullText) => {\r\n      userMessageIdRef.current = null\r\n      console.log('End of speech detected. Full text:', fullText);\r\n      stopTranscription()\r\n      if (fullText.trim()) {\r\n        console.log(\"_________________end_____________________\")\r\n        await handleStreamResponse(fullText);\r\n      }\r\n      startTranscription();\r\n    },\r\n    onFinished: () => {\r\n      if (joinedCallRef.current) {\r\n        console.log(\"_________________finished_____________________\")\r\n      }\r\n    },\r\n    onPartialResult: (finalTokens, nonFinalTokens) => {\r\n      const liveTranscript = [...finalTokens, ...nonFinalTokens].map((t) => t.text).join('');\r\n      console.log('Live transcript:', liveTranscript);\r\n      if (liveTranscript.trim()) {\r\n        if (!userMessageIdRef.current) {\r\n          userMessageIdRef.current = nanoid(8)\r\n          addMessage({\r\n            id: userMessageIdRef.current,\r\n            role: 'user',\r\n            content: liveTranscript\r\n          })\r\n        } else {\r\n          editMessage(userMessageIdRef.current, liveTranscript)\r\n        }\r\n      }\r\n    },\r\n  });\r\n\r\n const handleStreamResponse = async (userText: string) => {\r\n   try {\r\n     // Create bot message\r\n     const botId = nanoid(8);\r\n     botMessageIdRef.current = botId;\r\n     addMessage({\r\n       id: botId,\r\n       role: 'assistant',\r\n       content: ''\r\n     });\r\n\r\n     // Connect to TTS WebSocket\r\n     const ttsWs = new WebSocket('ws://*************:8000/tts'); // Adjust URL as needed\r\n     ttsWebSocketRef.current = ttsWs;\r\n\r\n     let fullResponse = '';\r\n\r\n     ttsWs.onopen = () => {\r\n       console.log('TTS WebSocket connected');\r\n     };\r\n\r\n     ttsWs.onmessage = (event) => {\r\n       const audioData = event.data;\r\n       if (audioData instanceof ArrayBuffer) {\r\n         audioBufferQueueRef.current.push(audioData);\r\n         playAudio();\r\n       }\r\n     };\r\n\r\n     ttsWs.onerror = (error) => {\r\n       console.error('TTS WebSocket error:', error);\r\n     };\r\n\r\n     ttsWs.onclose = () => {\r\n       console.log('TTS WebSocket closed');\r\n       setIsBotSpeaking(false);\r\n     };\r\n\r\n     // Fetch streaming response from chat API\r\n     const response = await fetch('/api/chat', {\r\n       method: 'POST',\r\n       headers: {\r\n         'Content-Type': 'application/json',\r\n       },\r\n       body: JSON.stringify({ message: userText }),\r\n     });\r\n\r\n     if (!response.ok) {\r\n       throw new Error('Failed to fetch chat response');\r\n     }\r\n\r\n     const reader = response.body?.getReader();\r\n     const decoder = new TextDecoder();\r\n\r\n     if (reader) {\r\n       let buffer = '';\r\n       while (true) {\r\n         const { done, value } = await reader.read();\r\n         if (done) break;\r\n\r\n         buffer += decoder.decode(value, { stream: true });\r\n         const lines = buffer.split('\\n');\r\n         buffer = lines.pop() || '';\r\n\r\n         for (const line of lines) {\r\n           if (line.startsWith('data: ')) {\r\n             const data = line.slice(6);\r\n             if (data === '[DONE]') break;\r\n\r\n             try {\r\n               const parsed = JSON.parse(data);\r\n               const content = parsed.choices[0]?.delta?.content || '';\r\n               if (content) {\r\n                 fullResponse += content;\r\n                 editMessage(botId, fullResponse);\r\n\r\n                 // Send to TTS\r\n                 if (ttsWs.readyState === WebSocket.OPEN) {\r\n                   ttsWs.send(content);\r\n                 }\r\n               }\r\n             } catch (e) {\r\n               console.error('Error parsing SSE data:', e);\r\n             }\r\n           }\r\n         }\r\n       }\r\n     }\r\n\r\n     // Close TTS WebSocket after response\r\n     if (ttsWs.readyState === WebSocket.OPEN) {\r\n       ttsWs.close();\r\n     }\r\n   } catch (error) {\r\n     console.error('Error in handleStreamResponse:', error);\r\n   }\r\n };\r\n\r\n const playAudio = async () => {\r\n   if (isPlayingRef.current || audioBufferQueueRef.current.length === 0) return;\r\n\r\n   isPlayingRef.current = true;\r\n   setIsBotSpeaking(true);\r\n\r\n   if (!audioContextRef.current) {\r\n     audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();\r\n   }\r\n\r\n   const context = audioContextRef.current;\r\n\r\n   while (audioBufferQueueRef.current.length > 0) {\r\n     const buffer = audioBufferQueueRef.current.shift();\r\n     if (buffer) {\r\n       try {\r\n         // Piper outputs 16-bit PCM at 22050 Hz, mono\r\n         const sampleRate = 22050;\r\n         const channels = 1;\r\n         const bytesPerSample = 2; // 16-bit\r\n\r\n         const numSamples = buffer.byteLength / bytesPerSample;\r\n         const audioBuffer = context.createBuffer(channels, numSamples, sampleRate);\r\n\r\n         // Convert 16-bit PCM to float32\r\n         const int16Array = new Int16Array(buffer);\r\n         const float32Array = new Float32Array(numSamples);\r\n         for (let i = 0; i < numSamples; i++) {\r\n           float32Array[i] = int16Array[i] / 32768.0; // Normalize to -1 to 1\r\n         }\r\n\r\n         audioBuffer.copyFromChannel(float32Array, 0);\r\n\r\n         const source = context.createBufferSource();\r\n         source.buffer = audioBuffer;\r\n         source.connect(context.destination);\r\n         source.start();\r\n\r\n         await new Promise(resolve => {\r\n           source.onended = resolve;\r\n         });\r\n       } catch (e) {\r\n         console.error('Error playing audio:', e);\r\n       }\r\n     }\r\n   }\r\n\r\n   isPlayingRef.current = false;\r\n   setIsBotSpeaking(false);\r\n };\r\n\r\n const audioContextRef = useRef<AudioContext | null>(null);\r\n  const analyserRef = useRef<AnalyserNode | null>(null);\r\n  const streamRef = useRef<MediaStream | null>(null);\r\n  const animationFrameIdRef = useRef<number | null>(null);\r\n\r\n  const stopAudioAnalysis = useCallback(() => {\r\n    if (animationFrameIdRef.current) {\r\n      cancelAnimationFrame(animationFrameIdRef.current);\r\n      animationFrameIdRef.current = null;\r\n    }\r\n    if (streamRef.current) {\r\n      streamRef.current.getTracks().forEach((track) => track.stop());\r\n      streamRef.current = null;\r\n    }\r\n    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {\r\n      audioContextRef.current.close();\r\n      audioContextRef.current = null;\r\n    }\r\n    setIsUserSpeaking(false);\r\n  }, []);\r\n\r\n  const startAudioAnalysis = useCallback(async () => {\r\n    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\r\n      try {\r\n        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n        streamRef.current = stream;\r\n\r\n        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\r\n        audioContextRef.current = audioContext;\r\n\r\n        const analyser = audioContext.createAnalyser();\r\n        analyser.fftSize = 512;\r\n        analyserRef.current = analyser;\r\n\r\n        const source = audioContext.createMediaStreamSource(stream);\r\n        source.connect(analyser);\r\n\r\n        const checkAudio = () => {\r\n          if (!analyserRef.current) return;\r\n\r\n          const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);\r\n          analyserRef.current.getByteFrequencyData(dataArray);\r\n\r\n          let sum = 0;\r\n          for (const amplitude of dataArray) {\r\n            sum += amplitude * amplitude;\r\n          }\r\n          const volume = Math.sqrt(sum / dataArray.length);\r\n\r\n          // Adjust this threshold to your needs\r\n          const speakingThreshold = 15;\r\n          setIsUserSpeaking(volume > speakingThreshold);\r\n\r\n          animationFrameIdRef.current = requestAnimationFrame(checkAudio);\r\n        };\r\n\r\n        checkAudio();\r\n      } catch (err) {\r\n        console.error('Error accessing microphone:', err);\r\n        setIsMicOn(false); // Turn mic off in UI if permission is denied\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isMicOn) {\r\n      startAudioAnalysis();\r\n    } else {\r\n      stopAudioAnalysis();\r\n    }\r\n\r\n    // Cleanup on component unmount\r\n    return () => {\r\n      stopAudioAnalysis();\r\n    };\r\n  }, [isMicOn, startAudioAnalysis, stopAudioAnalysis]);\r\n\r\n\r\n  return (\r\n    <div className=\"w-full h-full flex overflow-hidden\">\r\n      <div className=' bg-stone-900 h-full w-full overflow-hidden p-2 flex flex-col gap-2'>\r\n        <div className='flex gap-4 flex-1'>\r\n          <div\r\n            className={cn(\r\n              'bg-stone-700 h-full w-full overflow-hidden rounded-2xl flex justify-center items-center transition relative',\r\n              isUserSpeaking && 'ring-2 ring-emerald-500'\r\n            )}\r\n          >\r\n            <Avatar\r\n              avatarType='user'\r\n              size='lg'\r\n            />\r\n            <div\r\n              className='absolute bottom-2 w-60 h-30 bg-transparent rounded-lg overflow-hidden'\r\n            >\r\n              <div className='relative w-full h-full flex justify-center items-end overflow-hidden'>\r\n                <div\r\n                  className='absolute w-full h-full z-10 bg-gradient-to-b from-stone-900/80 via-transparent to-transparent'\r\n                />\r\n                <div className='w-full h-full flex flex-col justify-end'>\r\n                  <p>\r\n                    <span className='text-white'>\r\n                      {finalTokens.map((t) => t.text).join('')}\r\n                    </span>\r\n                    <span className='text-stone-400'>\r\n                      {nonFinalTokens.map((t) => t.text).join('')}\r\n                    </span>\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div\r\n            className={cn(\r\n              'bg-stone-700 h-full w-full overflow-hidden rounded-2xl flex justify-center items-center transition relative',\r\n              isBotSpeaking && 'ring-2 ring-emerald-500'\r\n            )}\r\n          >\r\n            <Avatar\r\n              avatarType='bot'\r\n              size='lg'\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className='h-12 rounded-2xl p-3 px-2 bg-stone-700 w-max self-center flex items-center justify-center gap-2'>\r\n          <button\r\n            className='text-white p-3 rounded-full bg-stone-900 hover:bg-stone-800 transition cursor-pointer'\r\n            onClick={() => {\r\n              setIsMicOn(!isMicOn)\r\n            }}\r\n          >\r\n            {isMicOn ? (\r\n              <MicIcon className='h-4 w-4' />\r\n            ) : (\r\n              <MicOffIcon className='h-4 w-4' />\r\n            )}\r\n          </button>\r\n          <button\r\n            className={cn(\r\n              'text-white p-3 rounded-full transition cursor-pointer',\r\n              joinedCall ? 'bg-red-900 hover:bg-red-800' : 'bg-emerald-600 hover:bg-emerald-500'\r\n            )}\r\n            onClick={() => {\r\n              toggleJoinCall()\r\n            }}\r\n          >\r\n            {joinedCall ? (\r\n              <PhoneOffIcon className='h-4 w-4' />\r\n            ) : (\r\n              <PhoneIcon className='h-4 w-4' />\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div className='bg-stone-800 h-full w-80 overflow-hidden overflow-y-auto flex flex-col p-3 gap-2'>\r\n        {messages.map((msg) => (\r\n          <ChatMessage key={msg.id} id={msg.id} role={msg.role} content={msg.content} />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default AiCall\r\n\r\ninterface ChatMessage {\r\n  id: string;\r\n  role: 'user' | 'assistant' | 'system';\r\n  content: string;\r\n}\r\n\r\nconst ChatMessage = memo(({\r\n  id,\r\n  role,\r\n  content\r\n}: ChatMessage) => {\r\n\r\n  if (role === 'system') {\r\n    return (\r\n      <div\r\n        key={id}\r\n        className='w-max self-center rounded-full bg-stone-700/80 px-3 py-0.5 text-xs text-stone-300'\r\n      >\r\n        {content}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div\r\n      key={id}\r\n      className={cn(\r\n        'w-max max-w-[90%] rounded-lg px-3 py-1 text-xs',\r\n        role === 'user' ? 'bg-amber-900 self-end text-white' : 'bg-transparent self-start text-white'\r\n      )}\r\n    >\r\n      {content}\r\n    </div>\r\n  )\r\n})"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AATA;;;;;;;;AAWA,SAAS;;IAEP,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,IAAA,yIAAO;IAEpE,MAAM,CAAC,YAAY,cAAc,GAAG,wKAAK,CAAC,QAAQ,CAAC;IACnD,MAAM,gBAAgB,IAAA,uKAAM,EAAC;IAC7B,IAAA,0KAAS;4BAAC;YACR,cAAc,OAAO,GAAG;QAC1B;2BAAG;QAAC;KAAW;IAEf,MAAM,CAAC,eAAe,iBAAiB,GAAG,wKAAK,CAAC,QAAQ,CAAC;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,wKAAK,CAAC,QAAQ,CAAC;IAE3D,MAAM,mBAAmB,IAAA,uKAAM,EAAgB;IAC/C,MAAM,kBAAkB,IAAA,uKAAM,EAAgB;IAE9C,MAAM,kBAAkB,IAAA,uKAAM,EAAmB;IACjD,MAAM,sBAAsB,IAAA,uKAAM,EAAgB,EAAE;IACpD,MAAM,eAAe,IAAA,uKAAM,EAAC;IAE5B,MAAM,CAAC,SAAS,WAAW,GAAG,wKAAK,CAAC,QAAQ,CAAC;IAE7C,SAAS;QACP,IAAI,CAAC,eAAe,MAAM;YACxB,WAAW;gBACT,IAAI,IAAA,uKAAM,EAAC;gBACX,MAAM;gBACN,SAAS;YACX;YACA;QACF,OAAO;YACL;YACA,WAAW;gBACT,IAAI,IAAA,uKAAM,EAAC;gBACX,MAAM;gBACN,SAAS;YACX;QACF;QACA,cAAc,CAAC;IACjB;IAEA,IAAA,0KAAS;4BAAC;YACR,WAAW;gBACT,IAAI,IAAA,uKAAM,EAAC;gBACX,MAAM;gBACN,SAAS;YACX;QACF;2BAAG,EAAE;IAEL,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,cAAc,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,GAAG,IAAA,+IAAa,EAAC;QAClG,QAAQ;QACR,aAAa;oCAAE,OAAO;gBACpB,iBAAiB,OAAO,GAAG;gBAC3B,QAAQ,GAAG,CAAC,sCAAsC;gBAClD;gBACA,IAAI,SAAS,IAAI,IAAI;oBACnB,QAAQ,GAAG,CAAC;oBACZ,MAAM,qBAAqB;gBAC7B;gBACA;YACF;;QACA,UAAU;oCAAE;gBACV,IAAI,cAAc,OAAO,EAAE;oBACzB,QAAQ,GAAG,CAAC;gBACd;YACF;;QACA,eAAe;oCAAE,CAAC,aAAa;gBAC7B,MAAM,iBAAiB;uBAAI;uBAAgB;iBAAe,CAAC,GAAG;2DAAC,CAAC,IAAM,EAAE,IAAI;0DAAE,IAAI,CAAC;gBACnF,QAAQ,GAAG,CAAC,oBAAoB;gBAChC,IAAI,eAAe,IAAI,IAAI;oBACzB,IAAI,CAAC,iBAAiB,OAAO,EAAE;wBAC7B,iBAAiB,OAAO,GAAG,IAAA,uKAAM,EAAC;wBAClC,WAAW;4BACT,IAAI,iBAAiB,OAAO;4BAC5B,MAAM;4BACN,SAAS;wBACX;oBACF,OAAO;wBACL,YAAY,iBAAiB,OAAO,EAAE;oBACxC;gBACF;YACF;;IACF;IAED,MAAM,uBAAuB,OAAO;QAClC,IAAI;gBAkDa;YAjDf,qBAAqB;YACrB,MAAM,QAAQ,IAAA,uKAAM,EAAC;YACrB,gBAAgB,OAAO,GAAG;YAC1B,WAAW;gBACT,IAAI;gBACJ,MAAM;gBACN,SAAS;YACX;YAEA,2BAA2B;YAC3B,MAAM,QAAQ,IAAI,UAAU,gCAAgC,uBAAuB;YACnF,gBAAgB,OAAO,GAAG;YAE1B,IAAI,eAAe;YAEnB,MAAM,MAAM,GAAG;gBACb,QAAQ,GAAG,CAAC;YACd;YAEA,MAAM,SAAS,GAAG,CAAC;gBACjB,MAAM,YAAY,MAAM,IAAI;gBAC5B,IAAI,qBAAqB,aAAa;oBACpC,oBAAoB,OAAO,CAAC,IAAI,CAAC;oBACjC;gBACF;YACF;YAEA,MAAM,OAAO,GAAG,CAAC;gBACf,QAAQ,KAAK,CAAC,wBAAwB;YACxC;YAEA,MAAM,OAAO,GAAG;gBACd,QAAQ,GAAG,CAAC;gBACZ,iBAAiB;YACnB;YAEA,yCAAyC;YACzC,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAS;YAC3C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAS,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,SAAS;YACvC,MAAM,UAAU,IAAI;YAEpB,IAAI,QAAQ;gBACV,IAAI,SAAS;gBACb,MAAO,KAAM;oBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;oBACzC,IAAI,MAAM;oBAEV,UAAU,QAAQ,MAAM,CAAC,OAAO;wBAAE,QAAQ;oBAAK;oBAC/C,MAAM,QAAQ,OAAO,KAAK,CAAC;oBAC3B,SAAS,MAAM,GAAG,MAAM;oBAExB,KAAK,MAAM,QAAQ,MAAO;wBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;4BAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;4BACxB,IAAI,SAAS,UAAU;4BAEvB,IAAI;oCAEc,wBAAA;gCADhB,MAAM,SAAS,KAAK,KAAK,CAAC;gCAC1B,MAAM,UAAU,EAAA,mBAAA,OAAO,OAAO,CAAC,EAAE,cAAjB,wCAAA,yBAAA,iBAAmB,KAAK,cAAxB,6CAAA,uBAA0B,OAAO,KAAI;gCACrD,IAAI,SAAS;oCACX,gBAAgB;oCAChB,YAAY,OAAO;oCAEnB,cAAc;oCACd,IAAI,MAAM,UAAU,KAAK,UAAU,IAAI,EAAE;wCACvC,MAAM,IAAI,CAAC;oCACb;gCACF;4BACF,EAAE,OAAO,GAAG;gCACV,QAAQ,KAAK,CAAC,2BAA2B;4BAC3C;wBACF;oBACF;gBACF;YACF;YAEA,qCAAqC;YACrC,IAAI,MAAM,UAAU,KAAK,UAAU,IAAI,EAAE;gBACvC,MAAM,KAAK;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,aAAa,OAAO,IAAI,oBAAoB,OAAO,CAAC,MAAM,KAAK,GAAG;QAEtE,aAAa,OAAO,GAAG;QACvB,iBAAiB;QAEjB,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,gBAAgB,OAAO,GAAG,IAAI,CAAC,OAAO,YAAY,IAAI,AAAC,OAAe,kBAAkB;QAC1F;QAEA,MAAM,UAAU,gBAAgB,OAAO;QAEvC,MAAO,oBAAoB,OAAO,CAAC,MAAM,GAAG,EAAG;YAC7C,MAAM,SAAS,oBAAoB,OAAO,CAAC,KAAK;YAChD,IAAI,QAAQ;gBACV,IAAI;oBACF,6CAA6C;oBAC7C,MAAM,aAAa;oBACnB,MAAM,WAAW;oBACjB,MAAM,iBAAiB,GAAG,SAAS;oBAEnC,MAAM,aAAa,OAAO,UAAU,GAAG;oBACvC,MAAM,cAAc,QAAQ,YAAY,CAAC,UAAU,YAAY;oBAE/D,gCAAgC;oBAChC,MAAM,aAAa,IAAI,WAAW;oBAClC,MAAM,eAAe,IAAI,aAAa;oBACtC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;wBACnC,YAAY,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,SAAS,uBAAuB;oBACpE;oBAEA,YAAY,eAAe,CAAC,cAAc;oBAE1C,MAAM,SAAS,QAAQ,kBAAkB;oBACzC,OAAO,MAAM,GAAG;oBAChB,OAAO,OAAO,CAAC,QAAQ,WAAW;oBAClC,OAAO,KAAK;oBAEZ,MAAM,IAAI,QAAQ,CAAA;wBAChB,OAAO,OAAO,GAAG;oBACnB;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,wBAAwB;gBACxC;YACF;QACF;QAEA,aAAa,OAAO,GAAG;QACvB,iBAAiB;IACnB;IAEA,MAAM,kBAAkB,IAAA,uKAAM,EAAsB;IACnD,MAAM,cAAc,IAAA,uKAAM,EAAsB;IAChD,MAAM,YAAY,IAAA,uKAAM,EAAqB;IAC7C,MAAM,sBAAsB,IAAA,uKAAM,EAAgB;IAElD,MAAM,oBAAoB,IAAA,4KAAW;iDAAC;YACpC,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,qBAAqB,oBAAoB,OAAO;gBAChD,oBAAoB,OAAO,GAAG;YAChC;YACA,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,SAAS,GAAG,OAAO;6DAAC,CAAC,QAAU,MAAM,IAAI;;gBAC3D,UAAU,OAAO,GAAG;YACtB;YACA,IAAI,gBAAgB,OAAO,IAAI,gBAAgB,OAAO,CAAC,KAAK,KAAK,UAAU;gBACzE,gBAAgB,OAAO,CAAC,KAAK;gBAC7B,gBAAgB,OAAO,GAAG;YAC5B;YACA,kBAAkB;QACpB;gDAAG,EAAE;IAEL,MAAM,qBAAqB,IAAA,4KAAW;kDAAC;YACrC,IAAI,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,YAAY,EAAE;gBACjE,IAAI;oBACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;wBAAE,OAAO;oBAAK;oBACvE,UAAU,OAAO,GAAG;oBAEpB,MAAM,eAAe,IAAI,CAAC,OAAO,YAAY,IAAI,AAAC,OAAe,kBAAkB;oBACnF,gBAAgB,OAAO,GAAG;oBAE1B,MAAM,WAAW,aAAa,cAAc;oBAC5C,SAAS,OAAO,GAAG;oBACnB,YAAY,OAAO,GAAG;oBAEtB,MAAM,SAAS,aAAa,uBAAuB,CAAC;oBACpD,OAAO,OAAO,CAAC;oBAEf,MAAM;6EAAa;4BACjB,IAAI,CAAC,YAAY,OAAO,EAAE;4BAE1B,MAAM,YAAY,IAAI,WAAW,YAAY,OAAO,CAAC,iBAAiB;4BACtE,YAAY,OAAO,CAAC,oBAAoB,CAAC;4BAEzC,IAAI,MAAM;4BACV,KAAK,MAAM,aAAa,UAAW;gCACjC,OAAO,YAAY;4BACrB;4BACA,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,UAAU,MAAM;4BAE/C,sCAAsC;4BACtC,MAAM,oBAAoB;4BAC1B,kBAAkB,SAAS;4BAE3B,oBAAoB,OAAO,GAAG,sBAAsB;wBACtD;;oBAEA;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,+BAA+B;oBAC7C,WAAW,QAAQ,6CAA6C;gBAClE;YACF;QACF;iDAAG,EAAE;IAEL,IAAA,0KAAS;4BAAC;YACR,IAAI,SAAS;gBACX;YACF,OAAO;gBACL;YACF;YAEA,+BAA+B;YAC/B;oCAAO;oBACL;gBACF;;QACF;2BAAG;QAAC;QAAS;QAAoB;KAAkB;IAGnD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAW,IAAA,4HAAE,EACX,+GACA,kBAAkB;;kDAGpB,6LAAC,0IAAM;wCACL,YAAW;wCACX,MAAK;;;;;;kDAEP,6LAAC;wCACC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EACb,YAAY,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,EAAE,IAAI,CAAC;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EACb,eAAe,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOpD,6LAAC;gCACC,WAAW,IAAA,4HAAE,EACX,+GACA,iBAAiB;0CAGnB,cAAA,6LAAC,0IAAM;oCACL,YAAW;oCACX,MAAK;;;;;;;;;;;;;;;;;kCAKX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS;oCACP,WAAW,CAAC;gCACd;0CAEC,wBACC,6LAAC,kNAAO;oCAAC,WAAU;;;;;yDAEnB,6LAAC,+NAAU;oCAAC,WAAU;;;;;;;;;;;0CAG1B,6LAAC;gCACC,WAAW,IAAA,4HAAE,EACX,yDACA,aAAa,gCAAgC;gCAE/C,SAAS;oCACP;gCACF;0CAEC,2BACC,6LAAC,qOAAY;oCAAC,WAAU;;;;;yDAExB,6LAAC,wNAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,oBACb,6LAAC;wBAAyB,IAAI,IAAI,EAAE;wBAAE,MAAM,IAAI,IAAI;wBAAE,SAAS,IAAI,OAAO;uBAAxD,IAAI,EAAE;;;;;;;;;;;;;;;;AAKlC;GA5YS;;QAEsD,yIAAO;QA+CkB,+IAAa;;;KAjD5F;uCA8YM;AAQf,MAAM,4BAAc,IAAA,qKAAI,EAAC;QAAC,EACxB,EAAE,EACF,IAAI,EACJ,OAAO,EACK;IAEZ,IAAI,SAAS,UAAU;QACrB,qBACE,6LAAC;YAEC,WAAU;sBAET;WAHI;;;;;IAMX;IAEA,qBACE,6LAAC;QAEC,WAAW,IAAA,4HAAE,EACX,kDACA,SAAS,SAAS,qCAAqC;kBAGxD;OANI;;;;;AASX;MA5BM", "debugId": null}}, {"offset": {"line": 1213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hackathon/vtb-hack/arc/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport AiCall from \"@/components/call\";\nimport Transcribe from \"@/components/transcribe\";\n\nexport default function Home() {\n  return (\n    <div className=\"w-full h-full overflow-y-auto overflow-x-hidden\">\n      <AiCall />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,wIAAM;;;;;;;;;;AAGb;KANwB", "debugId": null}}]}